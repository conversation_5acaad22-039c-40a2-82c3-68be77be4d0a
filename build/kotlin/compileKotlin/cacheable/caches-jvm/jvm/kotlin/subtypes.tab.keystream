kotlin.Enum2kotlinx.serialization.internal.GeneratedSerializercom.aicodingcli.ai.AiService com.aicodingcli.ai.BaseAiServicejava.lang.Exception*com.aicodingcli.code.analysis.CodeAnalyzercom.aicodingcli.plugins.Plugin'com.aicodingcli.plugins.AiServicePlugin%com.aicodingcli.plugins.CommandPlugin)com.aicodingcli.plugins.BaseCommandPlugin(com.aicodingcli.plugins.PluginPermission%com.aicodingcli.plugins.PluginContext$com.aicodingcli.plugins.PluginLoggerjava.lang.SecurityException"com.aicodingcli.plugins.PluginSpec!kotlinx.serialization.KSerializer+com.aicodingcli.conversation.TaskDecomposer                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            