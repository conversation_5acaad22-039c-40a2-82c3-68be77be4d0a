/ Header Record For PersistentHashMapValueStorage kotlin.Enum kotlin.Enum kotlin.Enum3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer com.aicodingcli.ai.AiService!  com.aicodingcli.ai.BaseAiService!  com.aicodingcli.ai.BaseAiService!  com.aicodingcli.ai.BaseAiService!  com.aicodingcli.ai.BaseAiService3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer java.lang.Exception!  com.aicodingcli.ai.BaseAiService3 2kotlinx.serialization.internal.GeneratedSerializer java.lang.Exception3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer java.lang.Exception!  com.aicodingcli.ai.BaseAiService com.aicodingcli.ai.AiService!  com.aicodingcli.ai.BaseAiService!  com.aicodingcli.ai.BaseAiService!  com.aicodingcli.ai.BaseAiService!  com.aicodingcli.ai.BaseAiService3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer java.lang.Exception!  com.aicodingcli.ai.BaseAiService com.aicodingcli.ai.AiService!  com.aicodingcli.ai.BaseAiService!  com.aicodingcli.ai.BaseAiService!  com.aicodingcli.ai.BaseAiService!  com.aicodingcli.ai.BaseAiService3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer java.lang.Exception!  com.aicodingcli.ai.BaseAiService com.aicodingcli.ai.AiService!  com.aicodingcli.ai.BaseAiService!  com.aicodingcli.ai.BaseAiService!  com.aicodingcli.ai.BaseAiService kotlin.Enum kotlin.Enum kotlin.Enum3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer com.aicodingcli.ai.AiService!  com.aicodingcli.ai.BaseAiService3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer java.lang.Exception!  com.aicodingcli.ai.BaseAiService3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer java.lang.Exception!  com.aicodingcli.ai.BaseAiService!  com.aicodingcli.ai.BaseAiService!  com.aicodingcli.ai.BaseAiService3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum+ *com.aicodingcli.code.analysis.CodeAnalyzer kotlin.Enum+ *com.aicodingcli.code.analysis.CodeAnalyzer+ *com.aicodingcli.code.analysis.CodeAnalyzer+ *com.aicodingcli.code.analysis.CodeAnalyzer com.aicodingcli.plugins.Plugin( 'com.aicodingcli.plugins.AiServicePlugin com.aicodingcli.plugins.Plugin& %com.aicodingcli.plugins.CommandPlugin* )com.aicodingcli.plugins.BaseCommandPlugin) (com.aicodingcli.plugins.PluginPermission) (com.aicodingcli.plugins.PluginPermission) (com.aicodingcli.plugins.PluginPermission) (com.aicodingcli.plugins.PluginPermission) (com.aicodingcli.plugins.PluginPermission kotlin.Enum java.lang.Exception java.lang.Exception kotlin.Enum& %com.aicodingcli.plugins.PluginContext% $com.aicodingcli.plugins.PluginLogger kotlin.Enum java.lang.SecurityException# "com.aicodingcli.plugins.PluginSpec# "com.aicodingcli.plugins.PluginSpec# "com.aicodingcli.plugins.PluginSpec& %com.aicodingcli.plugins.PluginContext com.aicodingcli.ai.AiService kotlin.Enum java.lang.SecurityException3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer kotlin.Enum3 2kotlinx.serialization.internal.GeneratedSerializer kotlin.Enum3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer" !kotlinx.serialization.KSerializer" !kotlinx.serialization.KSerializer, +com.aicodingcli.conversation.TaskDecomposer, +com.aicodingcli.conversation.TaskDecomposer, +com.aicodingcli.conversation.TaskDecomposer, +com.aicodingcli.conversation.TaskDecomposer3 2kotlinx.serialization.internal.GeneratedSerializer kotlin.Enum3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer kotlin.Enum3 2kotlinx.serialization.internal.GeneratedSerializer kotlin.Enum3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer/ .com.aicodingcli.conversation.RequirementParser/ .com.aicodingcli.conversation.RequirementParser* )com.aicodingcli.conversation.ToolExecutor* )com.aicodingcli.conversation.ToolExecutor3 2com.aicodingcli.conversation.tools.BaseToolHandler3 2com.aicodingcli.conversation.tools.BaseToolHandler3 2com.aicodingcli.conversation.tools.BaseToolHandler3 2com.aicodingcli.conversation.tools.BaseToolHandler3 2com.aicodingcli.conversation.tools.BaseToolHandler3 2com.aicodingcli.conversation.tools.BaseToolHandler3 2com.aicodingcli.conversation.tools.BaseToolHandler/ .com.aicodingcli.conversation.tools.ToolHandler* )com.aicodingcli.conversation.ToolExecutor3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer kotlin.Enum3 2kotlinx.serialization.internal.GeneratedSerializer kotlin.Enum3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer kotlin.Enum/ .com.aicodingcli.conversation.RequirementParser1 0com.aicodingcli.conversation.AutoExecutionEngine3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer kotlin.Enum3 2kotlinx.serialization.internal.GeneratedSerializer kotlin.Enum3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer kotlin.Enum/ .com.aicodingcli.conversation.RequirementParser1 0com.aicodingcli.conversation.AutoExecutionEngine, +com.aicodingcli.conversation.TaskDecomposer1 0com.aicodingcli.conversation.AutoExecutionEngine1 0com.aicodingcli.conversation.AutoExecutionEngine1 0com.aicodingcli.conversation.AutoExecutionEngine