/ Header Record For PersistentHashMapValueStorage kotlin.Enum kotlin.Enum kotlin.Enum3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer com.aicodingcli.ai.AiService!  com.aicodingcli.ai.BaseAiService3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer java.lang.Exception!  com.aicodingcli.ai.BaseAiService3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer java.lang.Exception!  com.aicodingcli.ai.BaseAiService3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer java.lang.Exception!  com.aicodingcli.ai.BaseAiService kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum+ *com.aicodingcli.code.analysis.CodeAnalyzer kotlin.Enum3 2kotlinx.serialization.internal.GeneratedSerializer1 0com.aicodingcli.conversation.AutoExecutionEngine, +com.aicodingcli.conversation.AiPromptEngine3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer, +com.aicodingcli.conversation.AnalysisResult, +com.aicodingcli.conversation.AnalysisResult, +com.aicodingcli.conversation.ActionDecision, +com.aicodingcli.conversation.ActionDecision, +com.aicodingcli.conversation.ActionDecision, +com.aicodingcli.conversation.ActionDecision, +com.aicodingcli.conversation.ActionDecision2 1com.aicodingcli.conversation.CompletionEvaluation2 1com.aicodingcli.conversation.CompletionEvaluation kotlin.Enum kotlin.Enum1 0com.aicodingcli.conversation.AutoExecutionEngine3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer kotlin.Enum3 2kotlinx.serialization.internal.GeneratedSerializer kotlin.Enum3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer kotlin.Enum3 2kotlinx.serialization.internal.GeneratedSerializer kotlin.Enum" !kotlinx.serialization.KSerializer" !kotlinx.serialization.KSerializer" !kotlinx.serialization.KSerializer/ .com.aicodingcli.conversation.RequirementParser, +com.aicodingcli.conversation.TaskDecomposer* )com.aicodingcli.conversation.ToolExecutor3 2com.aicodingcli.conversation.tools.BaseToolHandler3 2com.aicodingcli.conversation.tools.BaseToolHandler3 2com.aicodingcli.conversation.tools.BaseToolHandler3 2com.aicodingcli.conversation.tools.BaseToolHandler3 2com.aicodingcli.conversation.tools.BaseToolHandler3 2com.aicodingcli.conversation.tools.BaseToolHandler3 2com.aicodingcli.conversation.tools.BaseToolHandler3 2com.aicodingcli.conversation.tools.BaseToolHandler3 2com.aicodingcli.conversation.tools.BaseToolHandler3 2com.aicodingcli.conversation.tools.BaseToolHandler3 2com.aicodingcli.conversation.tools.BaseToolHandler3 2com.aicodingcli.conversation.tools.BaseToolHandler3 2com.aicodingcli.conversation.tools.BaseToolHandler3 2com.aicodingcli.conversation.tools.BaseToolHandler3 2com.aicodingcli.conversation.tools.BaseToolHandler/ .com.aicodingcli.conversation.tools.ToolHandler3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer java.lang.Exception com.aicodingcli.plugins.Plugin( 'com.aicodingcli.plugins.AiServicePlugin com.aicodingcli.plugins.Plugin& %com.aicodingcli.plugins.CommandPlugin* )com.aicodingcli.plugins.BaseCommandPlugin) (com.aicodingcli.plugins.PluginPermission) (com.aicodingcli.plugins.PluginPermission) (com.aicodingcli.plugins.PluginPermission) (com.aicodingcli.plugins.PluginPermission) (com.aicodingcli.plugins.PluginPermission kotlin.Enum java.lang.Exception java.lang.Exception kotlin.Enum& %com.aicodingcli.plugins.PluginContext% $com.aicodingcli.plugins.PluginLogger kotlin.Enum java.lang.SecurityException# "com.aicodingcli.plugins.PluginSpec# "com.aicodingcli.plugins.PluginSpec# "com.aicodingcli.plugins.PluginSpec& %com.aicodingcli.plugins.PluginContext com.aicodingcli.ai.AiService1 0com.aicodingcli.conversation.AutoExecutionEngine1 0com.aicodingcli.conversation.AutoExecutionEngine