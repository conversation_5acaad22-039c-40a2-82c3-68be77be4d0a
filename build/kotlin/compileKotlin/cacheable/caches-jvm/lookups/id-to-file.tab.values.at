/ Header Record For PersistentHashMapValueStorage( 'src/main/kotlin/com/aicodingcli/Main.kt/ .src/main/kotlin/com/aicodingcli/ai/AiModels.kt0 /src/main/kotlin/com/aicodingcli/ai/AiService.kt= <src/main/kotlin/com/aicodingcli/ai/providers/OpenAiModels.kt> =src/main/kotlin/com/aicodingcli/ai/providers/OpenAiService.kt4 3src/main/kotlin/com/aicodingcli/config/AppConfig.kt8 7src/main/kotlin/com/aicodingcli/config/ConfigManager.kt5 4src/main/kotlin/com/aicodingcli/http/AiHttpClient.kt3 2src/main/kotlin/com/aicodingcli/http/HttpModels.kt= <src/main/kotlin/com/aicodingcli/ai/providers/ClaudeModels.kt0 /src/main/kotlin/com/aicodingcli/ai/AiService.kt> =src/main/kotlin/com/aicodingcli/ai/providers/ClaudeService.kt( 'src/main/kotlin/com/aicodingcli/Main.kt0 /src/main/kotlin/com/aicodingcli/ai/AiService.kt= <src/main/kotlin/com/aicodingcli/ai/providers/OllamaModels.kt> =src/main/kotlin/com/aicodingcli/ai/providers/OllamaService.kt= <src/main/kotlin/com/aicodingcli/ai/providers/OllamaModels.kt> =src/main/kotlin/com/aicodingcli/ai/providers/OllamaService.kt( 'src/main/kotlin/com/aicodingcli/Main.kt0 /src/main/kotlin/com/aicodingcli/ai/AiService.kt( 'src/main/kotlin/com/aicodingcli/Main.kt/ .src/main/kotlin/com/aicodingcli/ai/AiModels.kt0 /src/main/kotlin/com/aicodingcli/ai/AiService.kt4 3src/main/kotlin/com/aicodingcli/config/AppConfig.kt8 7src/main/kotlin/com/aicodingcli/config/ConfigManager.kt( 'src/main/kotlin/com/aicodingcli/Main.kt( 'src/main/kotlin/com/aicodingcli/Main.kt( 'src/main/kotlin/com/aicodingcli/Main.kt= <src/main/kotlin/com/aicodingcli/ai/providers/OpenAiModels.kt> =src/main/kotlin/com/aicodingcli/ai/providers/OpenAiService.kt5 4src/main/kotlin/com/aicodingcli/http/AiHttpClient.kt= <src/main/kotlin/com/aicodingcli/ai/providers/ClaudeModels.kt> =src/main/kotlin/com/aicodingcli/ai/providers/ClaudeService.kt> =src/main/kotlin/com/aicodingcli/ai/providers/OllamaService.kt> =src/main/kotlin/com/aicodingcli/ai/providers/ClaudeService.kt5 4src/main/kotlin/com/aicodingcli/http/AiHttpClient.kt( 'src/main/kotlin/com/aicodingcli/Main.kt: 9src/main/kotlin/com/aicodingcli/history/HistoryManager.kt9 8src/main/kotlin/com/aicodingcli/history/HistoryModels.kt( 'src/main/kotlin/com/aicodingcli/Main.kt: 9src/main/kotlin/com/aicodingcli/history/HistoryManager.ktD Csrc/main/kotlin/com/aicodingcli/code/analysis/CodeAnalysisModels.kt> =src/main/kotlin/com/aicodingcli/code/analysis/CodeAnalyzer.ktC Bsrc/main/kotlin/com/aicodingcli/code/common/ProgrammingLanguage.kt> =src/main/kotlin/com/aicodingcli/code/analysis/CodeAnalyzer.kt> =src/main/kotlin/com/aicodingcli/code/analysis/CodeAnalyzer.ktB Asrc/main/kotlin/com/aicodingcli/code/metrics/MetricsCalculator.kt@ ?src/main/kotlin/com/aicodingcli/code/quality/QualityAnalyzer.kt( 'src/main/kotlin/com/aicodingcli/Main.kt> =src/main/kotlin/com/aicodingcli/code/analysis/CodeAnalyzer.kt( 'src/main/kotlin/com/aicodingcli/Main.ktB Asrc/main/kotlin/com/aicodingcli/code/metrics/MetricsCalculator.ktB Asrc/main/kotlin/com/aicodingcli/code/metrics/MetricsCalculator.kt@ ?src/main/kotlin/com/aicodingcli/code/quality/QualityAnalyzer.kt@ ?src/main/kotlin/com/aicodingcli/code/quality/QualityAnalyzer.kt( 'src/main/kotlin/com/aicodingcli/Main.kt; :src/main/kotlin/com/aicodingcli/plugins/AiServicePlugin.kt9 8src/main/kotlin/com/aicodingcli/plugins/CommandPlugin.kt2 1src/main/kotlin/com/aicodingcli/plugins/Plugin.kt9 8src/main/kotlin/com/aicodingcli/plugins/PluginCommand.kt9 8src/main/kotlin/com/aicodingcli/plugins/PluginContext.kt9 8src/main/kotlin/com/aicodingcli/plugins/PluginManager.kt: 9src/main/kotlin/com/aicodingcli/plugins/PluginRegistry.kt: 9src/main/kotlin/com/aicodingcli/plugins/PluginSecurity.ktC Bsrc/main/kotlin/com/aicodingcli/plugins/PluginTemplateGenerator.kt? >src/main/kotlin/com/aicodingcli/plugins/PluginTestFramework.kt: 9src/main/kotlin/com/aicodingcli/plugins/PluginSecurity.ktC Bsrc/main/kotlin/com/aicodingcli/conversation/ConversationModels.ktH Gsrc/main/kotlin/com/aicodingcli/conversation/ConversationSerializers.ktI Hsrc/main/kotlin/com/aicodingcli/conversation/ConversationStateManager.kt? >src/main/kotlin/com/aicodingcli/conversation/TaskDecomposer.kt? >src/main/kotlin/com/aicodingcli/conversation/TaskDecomposer.kt? >src/main/kotlin/com/aicodingcli/conversation/TaskDecomposer.kt? >src/main/kotlin/com/aicodingcli/conversation/TaskDecomposer.ktC Bsrc/main/kotlin/com/aicodingcli/conversation/ConversationModels.ktI Hsrc/main/kotlin/com/aicodingcli/conversation/ConversationStateManager.ktB Asrc/main/kotlin/com/aicodingcli/conversation/RequirementParser.ktB Asrc/main/kotlin/com/aicodingcli/conversation/RequirementParser.kt= <src/main/kotlin/com/aicodingcli/conversation/ToolExecutor.kt= <src/main/kotlin/com/aicodingcli/conversation/ToolExecutor.kt= <src/main/kotlin/com/aicodingcli/conversation/ToolExecutor.ktK Jsrc/main/kotlin/com/aicodingcli/conversation/tools/AnalysisToolHandlers.ktO Nsrc/main/kotlin/com/aicodingcli/conversation/tools/ExecutionMetadataManager.ktG Fsrc/main/kotlin/com/aicodingcli/conversation/tools/FileToolHandlers.ktB Asrc/main/kotlin/com/aicodingcli/conversation/tools/ToolHandler.ktC Bsrc/main/kotlin/com/aicodingcli/conversation/ConversationModels.ktI Hsrc/main/kotlin/com/aicodingcli/conversation/ConversationStateManager.ktB Asrc/main/kotlin/com/aicodingcli/conversation/RequirementParser.ktD Csrc/main/kotlin/com/aicodingcli/conversation/AutoExecutionEngine.ktC Bsrc/main/kotlin/com/aicodingcli/conversation/ConversationModels.ktI Hsrc/main/kotlin/com/aicodingcli/conversation/ConversationStateManager.ktB Asrc/main/kotlin/com/aicodingcli/conversation/RequirementParser.ktD Csrc/main/kotlin/com/aicodingcli/conversation/AutoExecutionEngine.kt? >src/main/kotlin/com/aicodingcli/conversation/TaskDecomposer.ktD Csrc/main/kotlin/com/aicodingcli/conversation/AutoExecutionEngine.ktD Csrc/main/kotlin/com/aicodingcli/conversation/AutoExecutionEngine.ktD Csrc/main/kotlin/com/aicodingcli/conversation/AutoExecutionEngine.ktD Csrc/main/kotlin/com/aicodingcli/conversation/AutoExecutionEngine.ktC Bsrc/main/kotlin/com/aicodingcli/conversation/ConversationModels.ktH Gsrc/main/kotlin/com/aicodingcli/conversation/ConversationSerializers.ktI Hsrc/main/kotlin/com/aicodingcli/conversation/ConversationStateManager.ktB Asrc/main/kotlin/com/aicodingcli/conversation/RequirementParser.kt? >src/main/kotlin/com/aicodingcli/conversation/TaskDecomposer.kt? >src/main/kotlin/com/aicodingcli/conversation/TaskDecomposer.ktD Csrc/main/kotlin/com/aicodingcli/conversation/AutoExecutionEngine.ktD Csrc/main/kotlin/com/aicodingcli/conversation/AutoExecutionEngine.ktD Csrc/main/kotlin/com/aicodingcli/conversation/AutoExecutionEngine.ktI Hsrc/main/kotlin/com/aicodingcli/conversation/ConversationStateManager.ktD Csrc/main/kotlin/com/aicodingcli/conversation/AutoExecutionEngine.kt