  AiCodingCli com.aicodingcli  AiCodingCliTest com.aicodingcli  AnalyzeCommandTest com.aicodingcli  ByteArrayOutputStream com.aicodingcli  File com.aicodingcli  PrintStream com.aicodingcli  System com.aicodingcli  TempDir com.aicodingcli  Test com.aicodingcli  arrayOf com.aicodingcli  assertEquals com.aicodingcli  
assertTrue com.aicodingcli  contains com.aicodingcli  trim com.aicodingcli  
trimIndent com.aicodingcli  	writeText com.aicodingcli  run com.aicodingcli.AiCodingCli  AiCodingCli com.aicodingcli.AiCodingCliTest  ByteArrayOutputStream com.aicodingcli.AiCodingCliTest  PrintStream com.aicodingcli.AiCodingCliTest  System com.aicodingcli.AiCodingCliTest  arrayOf com.aicodingcli.AiCodingCliTest  assertEquals com.aicodingcli.AiCodingCliTest  
assertTrue com.aicodingcli.AiCodingCliTest  contains com.aicodingcli.AiCodingCliTest  trim com.aicodingcli.AiCodingCliTest  
trimIndent com.aicodingcli.AiCodingCliTest  AiCodingCli "com.aicodingcli.AnalyzeCommandTest  ByteArrayOutputStream "com.aicodingcli.AnalyzeCommandTest  File "com.aicodingcli.AnalyzeCommandTest  PrintStream "com.aicodingcli.AnalyzeCommandTest  System "com.aicodingcli.AnalyzeCommandTest  arrayOf "com.aicodingcli.AnalyzeCommandTest  
assertTrue "com.aicodingcli.AnalyzeCommandTest  contains "com.aicodingcli.AnalyzeCommandTest  tempDir "com.aicodingcli.AnalyzeCommandTest  
trimIndent "com.aicodingcli.AnalyzeCommandTest  	writeText "com.aicodingcli.AnalyzeCommandTest  AiHttpClient com.aicodingcli.ai  	AiMessage com.aicodingcli.ai  
AiProvider com.aicodingcli.ai  	AiRequest com.aicodingcli.ai  AiRequestResponseTest com.aicodingcli.ai  
AiResponse com.aicodingcli.ai  	AiService com.aicodingcli.ai  AiServiceConfig com.aicodingcli.ai  AiServiceFactory com.aicodingcli.ai  AiServicePluginRegistry com.aicodingcli.ai  
AiServiceTest com.aicodingcli.ai  
AiStreamChunk com.aicodingcli.ai  
BaseAiService com.aicodingcli.ai  BaseAiServicePlugin com.aicodingcli.ai  
BeforeEach com.aicodingcli.ai  Boolean com.aicodingcli.ai  ClaudeException com.aicodingcli.ai  
ClaudeService com.aicodingcli.ai  FinishReason com.aicodingcli.ai  HttpResponse com.aicodingcli.ai  HttpStatusCode com.aicodingcli.ai  IllegalArgumentException com.aicodingcli.ai  List com.aicodingcli.ai  MessageRole com.aicodingcli.ai  OllamaException com.aicodingcli.ai  
OllamaService com.aicodingcli.ai  OpenAiException com.aicodingcli.ai  
OpenAiService com.aicodingcli.ai  PluginMetadata com.aicodingcli.ai  PluginTestFramework com.aicodingcli.ai  String com.aicodingcli.ai  Test com.aicodingcli.ai  
TestAiService com.aicodingcli.ai  TestAiServicePlugin com.aicodingcli.ai  
TokenUsage com.aicodingcli.ai  any com.aicodingcli.ai  assertDoesNotThrow com.aicodingcli.ai  assertEquals com.aicodingcli.ai  assertFalse com.aicodingcli.ai  
assertNotNull com.aicodingcli.ai  
assertNull com.aicodingcli.ai  assertThrows com.aicodingcli.ai  
assertTrue com.aicodingcli.ai  
claudeService com.aicodingcli.ai  coEvery com.aicodingcli.ai  coVerify com.aicodingcli.ai  com com.aicodingcli.ai  config com.aicodingcli.ai  contains com.aicodingcli.ai  
createService com.aicodingcli.ai  	emptyList com.aicodingcli.ai  
getAllPlugins com.aicodingcli.ai  	getPlugin com.aicodingcli.ai  
isNotEmpty com.aicodingcli.ai  isProviderSupported com.aicodingcli.ai  kotlinx com.aicodingcli.ai  last com.aicodingcli.ai  listOf com.aicodingcli.ai  mapOf com.aicodingcli.ai  mockHttpClient com.aicodingcli.ai  mockk com.aicodingcli.ai  
mutableListOf com.aicodingcli.ai  
ollamaService com.aicodingcli.ai  
openAiService com.aicodingcli.ai  register com.aicodingcli.ai  runBlocking com.aicodingcli.ai  runTest com.aicodingcli.ai  
testPlugin com.aicodingcli.ai  to com.aicodingcli.ai  toList com.aicodingcli.ai  
trimIndent com.aicodingcli.ai  
unregister com.aicodingcli.ai  content com.aicodingcli.ai.AiMessage  role com.aicodingcli.ai.AiMessage  CLAUDE com.aicodingcli.ai.AiProvider  OLLAMA com.aicodingcli.ai.AiProvider  OPENAI com.aicodingcli.ai.AiProvider  to com.aicodingcli.ai.AiProvider  	maxTokens com.aicodingcli.ai.AiRequest  messages com.aicodingcli.ai.AiRequest  model com.aicodingcli.ai.AiRequest  temperature com.aicodingcli.ai.AiRequest  	AiMessage (com.aicodingcli.ai.AiRequestResponseTest  	AiRequest (com.aicodingcli.ai.AiRequestResponseTest  
AiResponse (com.aicodingcli.ai.AiRequestResponseTest  
AiStreamChunk (com.aicodingcli.ai.AiRequestResponseTest  FinishReason (com.aicodingcli.ai.AiRequestResponseTest  MessageRole (com.aicodingcli.ai.AiRequestResponseTest  
TokenUsage (com.aicodingcli.ai.AiRequestResponseTest  assertEquals (com.aicodingcli.ai.AiRequestResponseTest  assertThrows (com.aicodingcli.ai.AiRequestResponseTest  coEvery (com.aicodingcli.ai.AiRequestResponseTest  kotlinx (com.aicodingcli.ai.AiRequestResponseTest  listOf (com.aicodingcli.ai.AiRequestResponseTest  mockk (com.aicodingcli.ai.AiRequestResponseTest  
mutableListOf (com.aicodingcli.ai.AiRequestResponseTest  runTest (com.aicodingcli.ai.AiRequestResponseTest  content com.aicodingcli.ai.AiResponse  finishReason com.aicodingcli.ai.AiResponse  model com.aicodingcli.ai.AiResponse  usage com.aicodingcli.ai.AiResponse  chat com.aicodingcli.ai.AiService  config com.aicodingcli.ai.AiService  
streamChat com.aicodingcli.ai.AiService  testConnection com.aicodingcli.ai.AiService  apiKey "com.aicodingcli.ai.AiServiceConfig  baseUrl "com.aicodingcli.ai.AiServiceConfig  copy "com.aicodingcli.ai.AiServiceConfig  model "com.aicodingcli.ai.AiServiceConfig  provider "com.aicodingcli.ai.AiServiceConfig  temperature "com.aicodingcli.ai.AiServiceConfig  
createService #com.aicodingcli.ai.AiServiceFactory  
AiProvider  com.aicodingcli.ai.AiServiceTest  AiServiceConfig  com.aicodingcli.ai.AiServiceTest  AiServiceFactory  com.aicodingcli.ai.AiServiceTest  assertDoesNotThrow  com.aicodingcli.ai.AiServiceTest  assertThrows  com.aicodingcli.ai.AiServiceTest  
createService  com.aicodingcli.ai.AiServiceTest  runTest  com.aicodingcli.ai.AiServiceTest  content  com.aicodingcli.ai.AiStreamChunk  finishReason  com.aicodingcli.ai.AiStreamChunk  validateRequest  com.aicodingcli.ai.BaseAiService  STOP com.aicodingcli.ai.FinishReason  	ASSISTANT com.aicodingcli.ai.MessageRole  SYSTEM com.aicodingcli.ai.MessageRole  USER com.aicodingcli.ai.MessageRole  completionTokens com.aicodingcli.ai.TokenUsage  promptTokens com.aicodingcli.ai.TokenUsage  totalTokens com.aicodingcli.ai.TokenUsage  
coroutines com.aicodingcli.ai.kotlinx  flow %com.aicodingcli.ai.kotlinx.coroutines  Flow *com.aicodingcli.ai.kotlinx.coroutines.flow  AiHttpClient com.aicodingcli.ai.providers  	AiMessage com.aicodingcli.ai.providers  
AiProvider com.aicodingcli.ai.providers  	AiRequest com.aicodingcli.ai.providers  AiServiceConfig com.aicodingcli.ai.providers  
BeforeEach com.aicodingcli.ai.providers  ClaudeException com.aicodingcli.ai.providers  
ClaudeService com.aicodingcli.ai.providers  ClaudeServiceTest com.aicodingcli.ai.providers  FinishReason com.aicodingcli.ai.providers  HttpResponse com.aicodingcli.ai.providers  HttpStatusCode com.aicodingcli.ai.providers  IllegalArgumentException com.aicodingcli.ai.providers  MessageRole com.aicodingcli.ai.providers  OllamaException com.aicodingcli.ai.providers  
OllamaService com.aicodingcli.ai.providers  OllamaServiceTest com.aicodingcli.ai.providers  OpenAiException com.aicodingcli.ai.providers  
OpenAiService com.aicodingcli.ai.providers  OpenAiServiceTest com.aicodingcli.ai.providers  Test com.aicodingcli.ai.providers  assertEquals com.aicodingcli.ai.providers  assertFalse com.aicodingcli.ai.providers  
assertNotNull com.aicodingcli.ai.providers  assertThrows com.aicodingcli.ai.providers  
assertTrue com.aicodingcli.ai.providers  
claudeService com.aicodingcli.ai.providers  coEvery com.aicodingcli.ai.providers  coVerify com.aicodingcli.ai.providers  com com.aicodingcli.ai.providers  config com.aicodingcli.ai.providers  contains com.aicodingcli.ai.providers  	emptyList com.aicodingcli.ai.providers  listOf com.aicodingcli.ai.providers  mapOf com.aicodingcli.ai.providers  mockHttpClient com.aicodingcli.ai.providers  mockk com.aicodingcli.ai.providers  
ollamaService com.aicodingcli.ai.providers  
openAiService com.aicodingcli.ai.providers  runTest com.aicodingcli.ai.providers  to com.aicodingcli.ai.providers  
trimIndent com.aicodingcli.ai.providers  chat *com.aicodingcli.ai.providers.ClaudeService  
streamChat *com.aicodingcli.ai.providers.ClaudeService  testConnection *com.aicodingcli.ai.providers.ClaudeService  	AiMessage .com.aicodingcli.ai.providers.ClaudeServiceTest  
AiProvider .com.aicodingcli.ai.providers.ClaudeServiceTest  	AiRequest .com.aicodingcli.ai.providers.ClaudeServiceTest  AiServiceConfig .com.aicodingcli.ai.providers.ClaudeServiceTest  
ClaudeService .com.aicodingcli.ai.providers.ClaudeServiceTest  FinishReason .com.aicodingcli.ai.providers.ClaudeServiceTest  HttpResponse .com.aicodingcli.ai.providers.ClaudeServiceTest  HttpStatusCode .com.aicodingcli.ai.providers.ClaudeServiceTest  MessageRole .com.aicodingcli.ai.providers.ClaudeServiceTest  assertEquals .com.aicodingcli.ai.providers.ClaudeServiceTest  assertFalse .com.aicodingcli.ai.providers.ClaudeServiceTest  
assertNotNull .com.aicodingcli.ai.providers.ClaudeServiceTest  assertThrows .com.aicodingcli.ai.providers.ClaudeServiceTest  
assertTrue .com.aicodingcli.ai.providers.ClaudeServiceTest  
claudeService .com.aicodingcli.ai.providers.ClaudeServiceTest  coEvery .com.aicodingcli.ai.providers.ClaudeServiceTest  coVerify .com.aicodingcli.ai.providers.ClaudeServiceTest  com .com.aicodingcli.ai.providers.ClaudeServiceTest  config .com.aicodingcli.ai.providers.ClaudeServiceTest  contains .com.aicodingcli.ai.providers.ClaudeServiceTest  	emptyList .com.aicodingcli.ai.providers.ClaudeServiceTest  listOf .com.aicodingcli.ai.providers.ClaudeServiceTest  mapOf .com.aicodingcli.ai.providers.ClaudeServiceTest  mockHttpClient .com.aicodingcli.ai.providers.ClaudeServiceTest  mockk .com.aicodingcli.ai.providers.ClaudeServiceTest  runTest .com.aicodingcli.ai.providers.ClaudeServiceTest  to .com.aicodingcli.ai.providers.ClaudeServiceTest  
trimIndent .com.aicodingcli.ai.providers.ClaudeServiceTest  chat *com.aicodingcli.ai.providers.OllamaService  
streamChat *com.aicodingcli.ai.providers.OllamaService  testConnection *com.aicodingcli.ai.providers.OllamaService  	AiMessage .com.aicodingcli.ai.providers.OllamaServiceTest  
AiProvider .com.aicodingcli.ai.providers.OllamaServiceTest  	AiRequest .com.aicodingcli.ai.providers.OllamaServiceTest  AiServiceConfig .com.aicodingcli.ai.providers.OllamaServiceTest  FinishReason .com.aicodingcli.ai.providers.OllamaServiceTest  HttpResponse .com.aicodingcli.ai.providers.OllamaServiceTest  HttpStatusCode .com.aicodingcli.ai.providers.OllamaServiceTest  MessageRole .com.aicodingcli.ai.providers.OllamaServiceTest  
OllamaService .com.aicodingcli.ai.providers.OllamaServiceTest  assertEquals .com.aicodingcli.ai.providers.OllamaServiceTest  assertFalse .com.aicodingcli.ai.providers.OllamaServiceTest  
assertNotNull .com.aicodingcli.ai.providers.OllamaServiceTest  assertThrows .com.aicodingcli.ai.providers.OllamaServiceTest  
assertTrue .com.aicodingcli.ai.providers.OllamaServiceTest  coEvery .com.aicodingcli.ai.providers.OllamaServiceTest  coVerify .com.aicodingcli.ai.providers.OllamaServiceTest  com .com.aicodingcli.ai.providers.OllamaServiceTest  config .com.aicodingcli.ai.providers.OllamaServiceTest  contains .com.aicodingcli.ai.providers.OllamaServiceTest  	emptyList .com.aicodingcli.ai.providers.OllamaServiceTest  listOf .com.aicodingcli.ai.providers.OllamaServiceTest  mapOf .com.aicodingcli.ai.providers.OllamaServiceTest  mockHttpClient .com.aicodingcli.ai.providers.OllamaServiceTest  mockk .com.aicodingcli.ai.providers.OllamaServiceTest  
ollamaService .com.aicodingcli.ai.providers.OllamaServiceTest  runTest .com.aicodingcli.ai.providers.OllamaServiceTest  to .com.aicodingcli.ai.providers.OllamaServiceTest  
trimIndent .com.aicodingcli.ai.providers.OllamaServiceTest  chat *com.aicodingcli.ai.providers.OpenAiService  
streamChat *com.aicodingcli.ai.providers.OpenAiService  testConnection *com.aicodingcli.ai.providers.OpenAiService  	AiMessage .com.aicodingcli.ai.providers.OpenAiServiceTest  
AiProvider .com.aicodingcli.ai.providers.OpenAiServiceTest  	AiRequest .com.aicodingcli.ai.providers.OpenAiServiceTest  AiServiceConfig .com.aicodingcli.ai.providers.OpenAiServiceTest  FinishReason .com.aicodingcli.ai.providers.OpenAiServiceTest  HttpResponse .com.aicodingcli.ai.providers.OpenAiServiceTest  HttpStatusCode .com.aicodingcli.ai.providers.OpenAiServiceTest  MessageRole .com.aicodingcli.ai.providers.OpenAiServiceTest  
OpenAiService .com.aicodingcli.ai.providers.OpenAiServiceTest  assertEquals .com.aicodingcli.ai.providers.OpenAiServiceTest  assertFalse .com.aicodingcli.ai.providers.OpenAiServiceTest  
assertNotNull .com.aicodingcli.ai.providers.OpenAiServiceTest  assertThrows .com.aicodingcli.ai.providers.OpenAiServiceTest  
assertTrue .com.aicodingcli.ai.providers.OpenAiServiceTest  coEvery .com.aicodingcli.ai.providers.OpenAiServiceTest  coVerify .com.aicodingcli.ai.providers.OpenAiServiceTest  com .com.aicodingcli.ai.providers.OpenAiServiceTest  config .com.aicodingcli.ai.providers.OpenAiServiceTest  contains .com.aicodingcli.ai.providers.OpenAiServiceTest  	emptyList .com.aicodingcli.ai.providers.OpenAiServiceTest  listOf .com.aicodingcli.ai.providers.OpenAiServiceTest  mapOf .com.aicodingcli.ai.providers.OpenAiServiceTest  mockHttpClient .com.aicodingcli.ai.providers.OpenAiServiceTest  mockk .com.aicodingcli.ai.providers.OpenAiServiceTest  
openAiService .com.aicodingcli.ai.providers.OpenAiServiceTest  runTest .com.aicodingcli.ai.providers.OpenAiServiceTest  to .com.aicodingcli.ai.providers.OpenAiServiceTest  
trimIndent .com.aicodingcli.ai.providers.OpenAiServiceTest  	DebugTest com.aicodingcli.code  DefaultCodeAnalyzer com.aicodingcli.code  	Exception com.aicodingcli.code  File com.aicodingcli.code  ProgrammingLanguage com.aicodingcli.code  ProjectAnalysisDebugTest com.aicodingcli.code  TempDir com.aicodingcli.code  Test com.aicodingcli.code  analyzer com.aicodingcli.code  forEach com.aicodingcli.code  println com.aicodingcli.code  runBlocking com.aicodingcli.code  tempDir com.aicodingcli.code  
trimIndent com.aicodingcli.code  	writeText com.aicodingcli.code  DefaultCodeAnalyzer com.aicodingcli.code.DebugTest  File com.aicodingcli.code.DebugTest  ProgrammingLanguage com.aicodingcli.code.DebugTest  analyzer com.aicodingcli.code.DebugTest  println com.aicodingcli.code.DebugTest  runBlocking com.aicodingcli.code.DebugTest  tempDir com.aicodingcli.code.DebugTest  
trimIndent com.aicodingcli.code.DebugTest  	writeText com.aicodingcli.code.DebugTest  DefaultCodeAnalyzer -com.aicodingcli.code.ProjectAnalysisDebugTest  File -com.aicodingcli.code.ProjectAnalysisDebugTest  analyzer -com.aicodingcli.code.ProjectAnalysisDebugTest  println -com.aicodingcli.code.ProjectAnalysisDebugTest  runBlocking -com.aicodingcli.code.ProjectAnalysisDebugTest  tempDir -com.aicodingcli.code.ProjectAnalysisDebugTest  
trimIndent -com.aicodingcli.code.ProjectAnalysisDebugTest  	writeText -com.aicodingcli.code.ProjectAnalysisDebugTest  AnalysisSummary com.aicodingcli.code.analysis  CodeAnalysisModelsTest com.aicodingcli.code.analysis  CodeAnalysisResult com.aicodingcli.code.analysis  CodeAnalyzerTest com.aicodingcli.code.analysis  	CodeIssue com.aicodingcli.code.analysis  CodeMetrics com.aicodingcli.code.analysis  DefaultCodeAnalyzer com.aicodingcli.code.analysis  
Dependency com.aicodingcli.code.analysis  DependencyScope com.aicodingcli.code.analysis  DependencyType com.aicodingcli.code.analysis  File com.aicodingcli.code.analysis  IllegalArgumentException com.aicodingcli.code.analysis  Improvement com.aicodingcli.code.analysis  ImprovementPriority com.aicodingcli.code.analysis  ImprovementType com.aicodingcli.code.analysis  
IssueSeverity com.aicodingcli.code.analysis  	IssueType com.aicodingcli.code.analysis  ProgrammingLanguage com.aicodingcli.code.analysis  ProjectAnalysisResult com.aicodingcli.code.analysis  String com.aicodingcli.code.analysis  TempDir com.aicodingcli.code.analysis  Test com.aicodingcli.code.analysis  analyzer com.aicodingcli.code.analysis  any com.aicodingcli.code.analysis  assertEquals com.aicodingcli.code.analysis  
assertNotNull com.aicodingcli.code.analysis  assertThrows com.aicodingcli.code.analysis  
assertTrue com.aicodingcli.code.analysis  createTempFile com.aicodingcli.code.analysis  	emptyList com.aicodingcli.code.analysis  
isNotEmpty com.aicodingcli.code.analysis  listOf com.aicodingcli.code.analysis  runBlocking com.aicodingcli.code.analysis  tempDir com.aicodingcli.code.analysis  
trimIndent com.aicodingcli.code.analysis  	writeText com.aicodingcli.code.analysis  averageComplexity -com.aicodingcli.code.analysis.AnalysisSummary  overallMaintainabilityIndex -com.aicodingcli.code.analysis.AnalysisSummary  
totalFiles -com.aicodingcli.code.analysis.AnalysisSummary  CodeAnalysisResult 4com.aicodingcli.code.analysis.CodeAnalysisModelsTest  	CodeIssue 4com.aicodingcli.code.analysis.CodeAnalysisModelsTest  CodeMetrics 4com.aicodingcli.code.analysis.CodeAnalysisModelsTest  
Dependency 4com.aicodingcli.code.analysis.CodeAnalysisModelsTest  DependencyScope 4com.aicodingcli.code.analysis.CodeAnalysisModelsTest  DependencyType 4com.aicodingcli.code.analysis.CodeAnalysisModelsTest  Improvement 4com.aicodingcli.code.analysis.CodeAnalysisModelsTest  ImprovementPriority 4com.aicodingcli.code.analysis.CodeAnalysisModelsTest  ImprovementType 4com.aicodingcli.code.analysis.CodeAnalysisModelsTest  
IssueSeverity 4com.aicodingcli.code.analysis.CodeAnalysisModelsTest  	IssueType 4com.aicodingcli.code.analysis.CodeAnalysisModelsTest  ProgrammingLanguage 4com.aicodingcli.code.analysis.CodeAnalysisModelsTest  assertEquals 4com.aicodingcli.code.analysis.CodeAnalysisModelsTest  
assertTrue 4com.aicodingcli.code.analysis.CodeAnalysisModelsTest  	emptyList 4com.aicodingcli.code.analysis.CodeAnalysisModelsTest  listOf 4com.aicodingcli.code.analysis.CodeAnalysisModelsTest  dependencies 0com.aicodingcli.code.analysis.CodeAnalysisResult  filePath 0com.aicodingcli.code.analysis.CodeAnalysisResult  issues 0com.aicodingcli.code.analysis.CodeAnalysisResult  language 0com.aicodingcli.code.analysis.CodeAnalysisResult  metrics 0com.aicodingcli.code.analysis.CodeAnalysisResult  suggestions 0com.aicodingcli.code.analysis.CodeAnalysisResult  DefaultCodeAnalyzer .com.aicodingcli.code.analysis.CodeAnalyzerTest  File .com.aicodingcli.code.analysis.CodeAnalyzerTest  ImprovementType .com.aicodingcli.code.analysis.CodeAnalyzerTest  	IssueType .com.aicodingcli.code.analysis.CodeAnalyzerTest  ProgrammingLanguage .com.aicodingcli.code.analysis.CodeAnalyzerTest  analyzer .com.aicodingcli.code.analysis.CodeAnalyzerTest  any .com.aicodingcli.code.analysis.CodeAnalyzerTest  assertEquals .com.aicodingcli.code.analysis.CodeAnalyzerTest  
assertNotNull .com.aicodingcli.code.analysis.CodeAnalyzerTest  assertThrows .com.aicodingcli.code.analysis.CodeAnalyzerTest  
assertTrue .com.aicodingcli.code.analysis.CodeAnalyzerTest  createTempFile .com.aicodingcli.code.analysis.CodeAnalyzerTest  
isNotEmpty .com.aicodingcli.code.analysis.CodeAnalyzerTest  runBlocking .com.aicodingcli.code.analysis.CodeAnalyzerTest  tempDir .com.aicodingcli.code.analysis.CodeAnalyzerTest  
trimIndent .com.aicodingcli.code.analysis.CodeAnalyzerTest  	writeText .com.aicodingcli.code.analysis.CodeAnalyzerTest  column 'com.aicodingcli.code.analysis.CodeIssue  line 'com.aicodingcli.code.analysis.CodeIssue  message 'com.aicodingcli.code.analysis.CodeIssue  severity 'com.aicodingcli.code.analysis.CodeIssue  
suggestion 'com.aicodingcli.code.analysis.CodeIssue  type 'com.aicodingcli.code.analysis.CodeIssue  cyclomaticComplexity )com.aicodingcli.code.analysis.CodeMetrics  duplicatedLines )com.aicodingcli.code.analysis.CodeMetrics  linesOfCode )com.aicodingcli.code.analysis.CodeMetrics  maintainabilityIndex )com.aicodingcli.code.analysis.CodeMetrics  testCoverage )com.aicodingcli.code.analysis.CodeMetrics  analyzeFile 1com.aicodingcli.code.analysis.DefaultCodeAnalyzer  analyzeProject 1com.aicodingcli.code.analysis.DefaultCodeAnalyzer  detectIssues 1com.aicodingcli.code.analysis.DefaultCodeAnalyzer  suggestImprovements 1com.aicodingcli.code.analysis.DefaultCodeAnalyzer  name (com.aicodingcli.code.analysis.Dependency  scope (com.aicodingcli.code.analysis.Dependency  type (com.aicodingcli.code.analysis.Dependency  version (com.aicodingcli.code.analysis.Dependency  COMPILE -com.aicodingcli.code.analysis.DependencyScope  EXTERNAL ,com.aicodingcli.code.analysis.DependencyType  description )com.aicodingcli.code.analysis.Improvement  line )com.aicodingcli.code.analysis.Improvement  priority )com.aicodingcli.code.analysis.Improvement  type )com.aicodingcli.code.analysis.Improvement  HIGH 1com.aicodingcli.code.analysis.ImprovementPriority  MAINTAINABILITY -com.aicodingcli.code.analysis.ImprovementType  PERFORMANCE -com.aicodingcli.code.analysis.ImprovementType  SECURITY -com.aicodingcli.code.analysis.ImprovementType  LOW +com.aicodingcli.code.analysis.IssueSeverity  MEDIUM +com.aicodingcli.code.analysis.IssueSeverity  
CODE_SMELL 'com.aicodingcli.code.analysis.IssueType  NAMING_CONVENTION 'com.aicodingcli.code.analysis.IssueType  PERFORMANCE 'com.aicodingcli.code.analysis.IssueType  SECURITY 'com.aicodingcli.code.analysis.IssueType  UNUSED_CODE 'com.aicodingcli.code.analysis.IssueType  fileResults 3com.aicodingcli.code.analysis.ProjectAnalysisResult  overallMetrics 3com.aicodingcli.code.analysis.ProjectAnalysisResult  projectPath 3com.aicodingcli.code.analysis.ProjectAnalysisResult  summary 3com.aicodingcli.code.analysis.ProjectAnalysisResult  IllegalArgumentException com.aicodingcli.code.common  ProgrammingLanguage com.aicodingcli.code.common  ProgrammingLanguageTest com.aicodingcli.code.common  Test com.aicodingcli.code.common  assertEquals com.aicodingcli.code.common  assertThrows com.aicodingcli.code.common  
assertTrue com.aicodingcli.code.common  fromFileExtension com.aicodingcli.code.common  fromFilePath com.aicodingcli.code.common  	lowercase com.aicodingcli.code.common  	Companion /com.aicodingcli.code.common.ProgrammingLanguage  JAVA /com.aicodingcli.code.common.ProgrammingLanguage  KOTLIN /com.aicodingcli.code.common.ProgrammingLanguage  PYTHON /com.aicodingcli.code.common.ProgrammingLanguage  
fileExtension /com.aicodingcli.code.common.ProgrammingLanguage  fromFileExtension /com.aicodingcli.code.common.ProgrammingLanguage  fromFilePath /com.aicodingcli.code.common.ProgrammingLanguage  name /com.aicodingcli.code.common.ProgrammingLanguage  supportsClasses /com.aicodingcli.code.common.ProgrammingLanguage  supportsInterfaces /com.aicodingcli.code.common.ProgrammingLanguage  fromFileExtension 9com.aicodingcli.code.common.ProgrammingLanguage.Companion  fromFilePath 9com.aicodingcli.code.common.ProgrammingLanguage.Companion  ProgrammingLanguage 3com.aicodingcli.code.common.ProgrammingLanguageTest  assertEquals 3com.aicodingcli.code.common.ProgrammingLanguageTest  assertThrows 3com.aicodingcli.code.common.ProgrammingLanguageTest  
assertTrue 3com.aicodingcli.code.common.ProgrammingLanguageTest  fromFileExtension 3com.aicodingcli.code.common.ProgrammingLanguageTest  fromFilePath 3com.aicodingcli.code.common.ProgrammingLanguageTest  	lowercase 3com.aicodingcli.code.common.ProgrammingLanguageTest  ImprovedMetricsCalculatorTest com.aicodingcli.code.metrics  MetricsCalculator com.aicodingcli.code.metrics  MetricsDebugTest com.aicodingcli.code.metrics  ProgrammingLanguage com.aicodingcli.code.metrics  Test com.aicodingcli.code.metrics  assertEquals com.aicodingcli.code.metrics  
assertTrue com.aicodingcli.code.metrics  forEachIndexed com.aicodingcli.code.metrics  isEmpty com.aicodingcli.code.metrics  lines com.aicodingcli.code.metrics  println com.aicodingcli.code.metrics  rangeTo com.aicodingcli.code.metrics  
startsWith com.aicodingcli.code.metrics  trim com.aicodingcli.code.metrics  
trimIndent com.aicodingcli.code.metrics  MetricsCalculator :com.aicodingcli.code.metrics.ImprovedMetricsCalculatorTest  ProgrammingLanguage :com.aicodingcli.code.metrics.ImprovedMetricsCalculatorTest  assertEquals :com.aicodingcli.code.metrics.ImprovedMetricsCalculatorTest  
assertTrue :com.aicodingcli.code.metrics.ImprovedMetricsCalculatorTest  
calculator :com.aicodingcli.code.metrics.ImprovedMetricsCalculatorTest  lines :com.aicodingcli.code.metrics.ImprovedMetricsCalculatorTest  rangeTo :com.aicodingcli.code.metrics.ImprovedMetricsCalculatorTest  
trimIndent :com.aicodingcli.code.metrics.ImprovedMetricsCalculatorTest  calculateMetrics .com.aicodingcli.code.metrics.MetricsCalculator  MetricsCalculator -com.aicodingcli.code.metrics.MetricsDebugTest  ProgrammingLanguage -com.aicodingcli.code.metrics.MetricsDebugTest  
calculator -com.aicodingcli.code.metrics.MetricsDebugTest  forEachIndexed -com.aicodingcli.code.metrics.MetricsDebugTest  isEmpty -com.aicodingcli.code.metrics.MetricsDebugTest  lines -com.aicodingcli.code.metrics.MetricsDebugTest  println -com.aicodingcli.code.metrics.MetricsDebugTest  
startsWith -com.aicodingcli.code.metrics.MetricsDebugTest  trim -com.aicodingcli.code.metrics.MetricsDebugTest  
trimIndent -com.aicodingcli.code.metrics.MetricsDebugTest  ImprovedQualityAnalyzerTest com.aicodingcli.code.quality  ImprovementType com.aicodingcli.code.quality  	IssueType com.aicodingcli.code.quality  ProgrammingLanguage com.aicodingcli.code.quality  QualityAnalyzer com.aicodingcli.code.quality  QualityAnalyzerTest com.aicodingcli.code.quality  Test com.aicodingcli.code.quality  any com.aicodingcli.code.quality  assertEquals com.aicodingcli.code.quality  assertFalse com.aicodingcli.code.quality  
assertTrue com.aicodingcli.code.quality  contains com.aicodingcli.code.quality  filter com.aicodingcli.code.quality  forEach com.aicodingcli.code.quality  isBlank com.aicodingcli.code.quality  
isNotEmpty com.aicodingcli.code.quality  println com.aicodingcli.code.quality  
trimIndent com.aicodingcli.code.quality  ImprovementType 8com.aicodingcli.code.quality.ImprovedQualityAnalyzerTest  	IssueType 8com.aicodingcli.code.quality.ImprovedQualityAnalyzerTest  ProgrammingLanguage 8com.aicodingcli.code.quality.ImprovedQualityAnalyzerTest  QualityAnalyzer 8com.aicodingcli.code.quality.ImprovedQualityAnalyzerTest  analyzer 8com.aicodingcli.code.quality.ImprovedQualityAnalyzerTest  any 8com.aicodingcli.code.quality.ImprovedQualityAnalyzerTest  assertEquals 8com.aicodingcli.code.quality.ImprovedQualityAnalyzerTest  assertFalse 8com.aicodingcli.code.quality.ImprovedQualityAnalyzerTest  
assertTrue 8com.aicodingcli.code.quality.ImprovedQualityAnalyzerTest  contains 8com.aicodingcli.code.quality.ImprovedQualityAnalyzerTest  filter 8com.aicodingcli.code.quality.ImprovedQualityAnalyzerTest  isBlank 8com.aicodingcli.code.quality.ImprovedQualityAnalyzerTest  
isNotEmpty 8com.aicodingcli.code.quality.ImprovedQualityAnalyzerTest  
trimIndent 8com.aicodingcli.code.quality.ImprovedQualityAnalyzerTest  detectIssues ,com.aicodingcli.code.quality.QualityAnalyzer  suggestImprovements ,com.aicodingcli.code.quality.QualityAnalyzer  ImprovementType 0com.aicodingcli.code.quality.QualityAnalyzerTest  ProgrammingLanguage 0com.aicodingcli.code.quality.QualityAnalyzerTest  QualityAnalyzer 0com.aicodingcli.code.quality.QualityAnalyzerTest  analyzer 0com.aicodingcli.code.quality.QualityAnalyzerTest  any 0com.aicodingcli.code.quality.QualityAnalyzerTest  
assertTrue 0com.aicodingcli.code.quality.QualityAnalyzerTest  
isNotEmpty 0com.aicodingcli.code.quality.QualityAnalyzerTest  println 0com.aicodingcli.code.quality.QualityAnalyzerTest  
trimIndent 0com.aicodingcli.code.quality.QualityAnalyzerTest  
AiProvider com.aicodingcli.config  	AppConfig com.aicodingcli.config  
BeforeEach com.aicodingcli.config  
ConfigManager com.aicodingcli.config  ConfigManagerTest com.aicodingcli.config  File com.aicodingcli.config  TempDir com.aicodingcli.config  Test com.aicodingcli.config  assertEquals com.aicodingcli.config  
assertNotNull com.aicodingcli.config  
assertTrue com.aicodingcli.config  com com.aicodingcli.config  
configManager com.aicodingcli.config  
isNotEmpty com.aicodingcli.config  mapOf com.aicodingcli.config  runTest com.aicodingcli.config  tempDir com.aicodingcli.config  to com.aicodingcli.config  
trimIndent com.aicodingcli.config  	writeText com.aicodingcli.config  defaultProvider  com.aicodingcli.config.AppConfig  	providers  com.aicodingcli.config.AppConfig  getCurrentProviderConfig $com.aicodingcli.config.ConfigManager  
loadConfig $com.aicodingcli.config.ConfigManager  
saveConfig $com.aicodingcli.config.ConfigManager  setDefaultProvider $com.aicodingcli.config.ConfigManager  updateProviderConfig $com.aicodingcli.config.ConfigManager  
AiProvider (com.aicodingcli.config.ConfigManagerTest  	AppConfig (com.aicodingcli.config.ConfigManagerTest  
ConfigManager (com.aicodingcli.config.ConfigManagerTest  File (com.aicodingcli.config.ConfigManagerTest  assertEquals (com.aicodingcli.config.ConfigManagerTest  
assertNotNull (com.aicodingcli.config.ConfigManagerTest  
assertTrue (com.aicodingcli.config.ConfigManagerTest  com (com.aicodingcli.config.ConfigManagerTest  
configManager (com.aicodingcli.config.ConfigManagerTest  
isNotEmpty (com.aicodingcli.config.ConfigManagerTest  mapOf (com.aicodingcli.config.ConfigManagerTest  runTest (com.aicodingcli.config.ConfigManagerTest  tempDir (com.aicodingcli.config.ConfigManagerTest  to (com.aicodingcli.config.ConfigManagerTest  
trimIndent (com.aicodingcli.config.ConfigManagerTest  	writeText (com.aicodingcli.config.ConfigManagerTest  	AfterEach com.aicodingcli.history  
AiProvider com.aicodingcli.history  
BeforeEach com.aicodingcli.history  ConversationMessage com.aicodingcli.history  ConversationSession com.aicodingcli.history  File com.aicodingcli.history  Files com.aicodingcli.history  HistoryManager com.aicodingcli.history  HistoryManagerTest com.aicodingcli.history  HistoryModelsTest com.aicodingcli.history  HistorySearchCriteria com.aicodingcli.history  HistoryStatistics com.aicodingcli.history  IllegalArgumentException com.aicodingcli.history  Instant com.aicodingcli.history  MessageRole com.aicodingcli.history  MessageTokenUsage com.aicodingcli.history  Test com.aicodingcli.history  Thread com.aicodingcli.history  all com.aicodingcli.history  assertEquals com.aicodingcli.history  assertFalse com.aicodingcli.history  
assertNotNull com.aicodingcli.history  
assertNull com.aicodingcli.history  assertThrows com.aicodingcli.history  
assertTrue com.aicodingcli.history  contains com.aicodingcli.history  deleteRecursively com.aicodingcli.history  emptyMap com.aicodingcli.history  mapOf com.aicodingcli.history  repeat com.aicodingcli.history  take com.aicodingcli.history  to com.aicodingcli.history  content +com.aicodingcli.history.ConversationMessage  id +com.aicodingcli.history.ConversationMessage  role +com.aicodingcli.history.ConversationMessage  	timestamp +com.aicodingcli.history.ConversationMessage  
tokenUsage +com.aicodingcli.history.ConversationMessage  
addMessage +com.aicodingcli.history.ConversationSession  	createdAt +com.aicodingcli.history.ConversationSession  getLastAssistantMessage +com.aicodingcli.history.ConversationSession  getLastUserMessage +com.aicodingcli.history.ConversationSession  
getSummary +com.aicodingcli.history.ConversationSession  id +com.aicodingcli.history.ConversationSession  messages +com.aicodingcli.history.ConversationSession  model +com.aicodingcli.history.ConversationSession  provider +com.aicodingcli.history.ConversationSession  title +com.aicodingcli.history.ConversationSession  	updatedAt +com.aicodingcli.history.ConversationSession  
addMessage &com.aicodingcli.history.HistoryManager  clearAllConversations &com.aicodingcli.history.HistoryManager  createConversation &com.aicodingcli.history.HistoryManager  deleteConversation &com.aicodingcli.history.HistoryManager  getAllConversations &com.aicodingcli.history.HistoryManager  getConversation &com.aicodingcli.history.HistoryManager  
getStatistics &com.aicodingcli.history.HistoryManager  searchConversations &com.aicodingcli.history.HistoryManager  
AiProvider *com.aicodingcli.history.HistoryManagerTest  Files *com.aicodingcli.history.HistoryManagerTest  HistoryManager *com.aicodingcli.history.HistoryManagerTest  HistorySearchCriteria *com.aicodingcli.history.HistoryManagerTest  MessageRole *com.aicodingcli.history.HistoryManagerTest  MessageTokenUsage *com.aicodingcli.history.HistoryManagerTest  Thread *com.aicodingcli.history.HistoryManagerTest  all *com.aicodingcli.history.HistoryManagerTest  assertEquals *com.aicodingcli.history.HistoryManagerTest  assertFalse *com.aicodingcli.history.HistoryManagerTest  
assertNotNull *com.aicodingcli.history.HistoryManagerTest  
assertNull *com.aicodingcli.history.HistoryManagerTest  assertThrows *com.aicodingcli.history.HistoryManagerTest  
assertTrue *com.aicodingcli.history.HistoryManagerTest  contains *com.aicodingcli.history.HistoryManagerTest  deleteRecursively *com.aicodingcli.history.HistoryManagerTest  historyManager *com.aicodingcli.history.HistoryManagerTest  repeat *com.aicodingcli.history.HistoryManagerTest  take *com.aicodingcli.history.HistoryManagerTest  tempDir *com.aicodingcli.history.HistoryManagerTest  
AiProvider )com.aicodingcli.history.HistoryModelsTest  ConversationMessage )com.aicodingcli.history.HistoryModelsTest  ConversationSession )com.aicodingcli.history.HistoryModelsTest  HistorySearchCriteria )com.aicodingcli.history.HistoryModelsTest  HistoryStatistics )com.aicodingcli.history.HistoryModelsTest  Instant )com.aicodingcli.history.HistoryModelsTest  MessageRole )com.aicodingcli.history.HistoryModelsTest  MessageTokenUsage )com.aicodingcli.history.HistoryModelsTest  assertEquals )com.aicodingcli.history.HistoryModelsTest  
assertNotNull )com.aicodingcli.history.HistoryModelsTest  
assertNull )com.aicodingcli.history.HistoryModelsTest  assertThrows )com.aicodingcli.history.HistoryModelsTest  
assertTrue )com.aicodingcli.history.HistoryModelsTest  contains )com.aicodingcli.history.HistoryModelsTest  emptyMap )com.aicodingcli.history.HistoryModelsTest  mapOf )com.aicodingcli.history.HistoryModelsTest  to )com.aicodingcli.history.HistoryModelsTest  fromDate -com.aicodingcli.history.HistorySearchCriteria  limit -com.aicodingcli.history.HistorySearchCriteria  model -com.aicodingcli.history.HistorySearchCriteria  provider -com.aicodingcli.history.HistorySearchCriteria  query -com.aicodingcli.history.HistorySearchCriteria  toDate -com.aicodingcli.history.HistorySearchCriteria  newestConversation )com.aicodingcli.history.HistoryStatistics  oldestConversation )com.aicodingcli.history.HistoryStatistics  providerBreakdown )com.aicodingcli.history.HistoryStatistics  totalConversations )com.aicodingcli.history.HistoryStatistics  
totalMessages )com.aicodingcli.history.HistoryStatistics  totalTokensUsed )com.aicodingcli.history.HistoryStatistics  completionTokens )com.aicodingcli.history.MessageTokenUsage  promptTokens )com.aicodingcli.history.MessageTokenUsage  totalTokens )com.aicodingcli.history.MessageTokenUsage  AiHttpClient com.aicodingcli.http  ByteReadChannel com.aicodingcli.http  	Exception com.aicodingcli.http  HttpClientTest com.aicodingcli.http  
HttpException com.aicodingcli.http  HttpHeaders com.aicodingcli.http  
HttpMethod com.aicodingcli.http  HttpResponse com.aicodingcli.http  HttpStatusCode com.aicodingcli.http  
MockEngine com.aicodingcli.http  RetryConfig com.aicodingcli.http  Test com.aicodingcli.http  assertEquals com.aicodingcli.http  assertThrows com.aicodingcli.http  	headersOf com.aicodingcli.http  kotlinx com.aicodingcli.http  mapOf com.aicodingcli.http  respond com.aicodingcli.http  runTest com.aicodingcli.http  to com.aicodingcli.http  get !com.aicodingcli.http.AiHttpClient  post !com.aicodingcli.http.AiHttpClient  AiHttpClient #com.aicodingcli.http.HttpClientTest  ByteReadChannel #com.aicodingcli.http.HttpClientTest  	Exception #com.aicodingcli.http.HttpClientTest  HttpHeaders #com.aicodingcli.http.HttpClientTest  
HttpMethod #com.aicodingcli.http.HttpClientTest  HttpStatusCode #com.aicodingcli.http.HttpClientTest  
MockEngine #com.aicodingcli.http.HttpClientTest  RetryConfig #com.aicodingcli.http.HttpClientTest  assertEquals #com.aicodingcli.http.HttpClientTest  assertThrows #com.aicodingcli.http.HttpClientTest  	headersOf #com.aicodingcli.http.HttpClientTest  kotlinx #com.aicodingcli.http.HttpClientTest  mapOf #com.aicodingcli.http.HttpClientTest  respond #com.aicodingcli.http.HttpClientTest  runTest #com.aicodingcli.http.HttpClientTest  to #com.aicodingcli.http.HttpClientTest  body !com.aicodingcli.http.HttpResponse  headers !com.aicodingcli.http.HttpResponse  status !com.aicodingcli.http.HttpResponse  	AiMessage com.aicodingcli.plugins  
AiProvider com.aicodingcli.plugins  	AiRequest com.aicodingcli.plugins  
AiResponse com.aicodingcli.plugins  	AiService com.aicodingcli.plugins  AiServiceConfig com.aicodingcli.plugins  AiServiceFactory com.aicodingcli.plugins  AiServicePlugin com.aicodingcli.plugins  AiServicePluginRegistry com.aicodingcli.plugins  AiServicePluginTest com.aicodingcli.plugins  
AiStreamChunk com.aicodingcli.plugins  
BaseAiService com.aicodingcli.plugins  BaseAiServicePlugin com.aicodingcli.plugins  BaseCommandPlugin com.aicodingcli.plugins  
BeforeEach com.aicodingcli.plugins  Boolean com.aicodingcli.plugins  CommandArgs com.aicodingcli.plugins  
CommandOption com.aicodingcli.plugins  
CommandPlugin com.aicodingcli.plugins  CommandPluginTest com.aicodingcli.plugins  
CommandResult com.aicodingcli.plugins  
ConfigManager com.aicodingcli.plugins  	Exception com.aicodingcli.plugins  File com.aicodingcli.plugins  FinishReason com.aicodingcli.plugins  HistoryManager com.aicodingcli.plugins  List com.aicodingcli.plugins  MessageRole com.aicodingcli.plugins  NotImplementedError com.aicodingcli.plugins  Path com.aicodingcli.plugins  Plugin com.aicodingcli.plugins  
PluginCommand com.aicodingcli.plugins  
PluginContext com.aicodingcli.plugins  PluginDependency com.aicodingcli.plugins  PluginDiscoveryService com.aicodingcli.plugins  PluginEvent com.aicodingcli.plugins  PluginEventDispatcher com.aicodingcli.plugins  PluginEventHandler com.aicodingcli.plugins  PluginEventType com.aicodingcli.plugins  PluginExecutionException com.aicodingcli.plugins  
PluginInfo com.aicodingcli.plugins  PluginLoadException com.aicodingcli.plugins  PluginLogger com.aicodingcli.plugins  
PluginManager com.aicodingcli.plugins  PluginManagerTest com.aicodingcli.plugins  PluginMetadata com.aicodingcli.plugins  PluginOperation com.aicodingcli.plugins  PluginOperationType com.aicodingcli.plugins  PluginPermission com.aicodingcli.plugins  PluginRegistry com.aicodingcli.plugins  PluginRegistryStatistics com.aicodingcli.plugins  
PluginSandbox com.aicodingcli.plugins  PluginSandboxInfo com.aicodingcli.plugins  PluginSecurityException com.aicodingcli.plugins  PluginSecurityManager com.aicodingcli.plugins  PluginSecurityPolicy com.aicodingcli.plugins  PluginSecurityTest com.aicodingcli.plugins  PluginState com.aicodingcli.plugins  
PluginTest com.aicodingcli.plugins  PluginTestFramework com.aicodingcli.plugins  PluginTestResult com.aicodingcli.plugins  PluginValidationResult com.aicodingcli.plugins  RuntimeException com.aicodingcli.plugins  SecurityException com.aicodingcli.plugins  String com.aicodingcli.plugins  System com.aicodingcli.plugins  TempDir com.aicodingcli.plugins  Test com.aicodingcli.plugins  
TestAiService com.aicodingcli.plugins  TestAiServicePlugin com.aicodingcli.plugins  TestCommandPlugin com.aicodingcli.plugins  TestPluginContext com.aicodingcli.plugins  
TokenUsage com.aicodingcli.plugins  any com.aicodingcli.plugins  arrayOf com.aicodingcli.plugins  assertEquals com.aicodingcli.plugins  assertFalse com.aicodingcli.plugins  
assertNotNull com.aicodingcli.plugins  
assertNull com.aicodingcli.plugins  assertThrows com.aicodingcli.plugins  
assertTrue com.aicodingcli.plugins  com com.aicodingcli.plugins  contains com.aicodingcli.plugins  createDefaultPolicy com.aicodingcli.plugins  	emptyList com.aicodingcli.plugins  error com.aicodingcli.plugins  failure com.aicodingcli.plugins  filterIsInstance com.aicodingcli.plugins  first com.aicodingcli.plugins  
getAllPlugins com.aicodingcli.plugins  	getPlugin com.aicodingcli.plugins  
isNotEmpty com.aicodingcli.plugins  isProviderSupported com.aicodingcli.plugins  java com.aicodingcli.plugins  joinToString com.aicodingcli.plugins  kotlinx com.aicodingcli.plugins  last com.aicodingcli.plugins  listOf com.aicodingcli.plugins  map com.aicodingcli.plugins  mapOf com.aicodingcli.plugins  
pluginManager com.aicodingcli.plugins  register com.aicodingcli.plugins  runBlocking com.aicodingcli.plugins  success com.aicodingcli.plugins  
testFramework com.aicodingcli.plugins  
testPlugin com.aicodingcli.plugins  to com.aicodingcli.plugins  toList com.aicodingcli.plugins  
unregister com.aicodingcli.plugins  	uppercase com.aicodingcli.plugins  validatePermissionRequest com.aicodingcli.plugins  	writeText com.aicodingcli.plugins  metadata 'com.aicodingcli.plugins.AiServicePlugin  validateConfig 'com.aicodingcli.plugins.AiServicePlugin  
getAllPlugins /com.aicodingcli.plugins.AiServicePluginRegistry  	getPlugin /com.aicodingcli.plugins.AiServicePluginRegistry  isProviderSupported /com.aicodingcli.plugins.AiServicePluginRegistry  register /com.aicodingcli.plugins.AiServicePluginRegistry  
unregister /com.aicodingcli.plugins.AiServicePluginRegistry  	AiMessage +com.aicodingcli.plugins.AiServicePluginTest  
AiProvider +com.aicodingcli.plugins.AiServicePluginTest  	AiRequest +com.aicodingcli.plugins.AiServicePluginTest  AiServiceConfig +com.aicodingcli.plugins.AiServicePluginTest  AiServicePluginRegistry +com.aicodingcli.plugins.AiServicePluginTest  FinishReason +com.aicodingcli.plugins.AiServicePluginTest  MessageRole +com.aicodingcli.plugins.AiServicePluginTest  PluginTestFramework +com.aicodingcli.plugins.AiServicePluginTest  TestAiServicePlugin +com.aicodingcli.plugins.AiServicePluginTest  any +com.aicodingcli.plugins.AiServicePluginTest  assertEquals +com.aicodingcli.plugins.AiServicePluginTest  assertFalse +com.aicodingcli.plugins.AiServicePluginTest  
assertNotNull +com.aicodingcli.plugins.AiServicePluginTest  
assertNull +com.aicodingcli.plugins.AiServicePluginTest  
assertTrue +com.aicodingcli.plugins.AiServicePluginTest  contains +com.aicodingcli.plugins.AiServicePluginTest  
getAllPlugins +com.aicodingcli.plugins.AiServicePluginTest  	getPlugin +com.aicodingcli.plugins.AiServicePluginTest  
isNotEmpty +com.aicodingcli.plugins.AiServicePluginTest  isProviderSupported +com.aicodingcli.plugins.AiServicePluginTest  listOf +com.aicodingcli.plugins.AiServicePluginTest  register +com.aicodingcli.plugins.AiServicePluginTest  runBlocking +com.aicodingcli.plugins.AiServicePluginTest  
testFramework +com.aicodingcli.plugins.AiServicePluginTest  
testPlugin +com.aicodingcli.plugins.AiServicePluginTest  toList +com.aicodingcli.plugins.AiServicePluginTest  
unregister +com.aicodingcli.plugins.AiServicePluginTest  getDefaultConfig +com.aicodingcli.plugins.BaseAiServicePlugin  
initialize +com.aicodingcli.plugins.BaseAiServicePlugin  shutdown +com.aicodingcli.plugins.BaseAiServicePlugin  validateProviderConfig +com.aicodingcli.plugins.BaseAiServicePlugin  
createCommand )com.aicodingcli.plugins.BaseCommandPlugin  
initialize )com.aicodingcli.plugins.BaseCommandPlugin  shutdown )com.aicodingcli.plugins.BaseCommandPlugin  args #com.aicodingcli.plugins.CommandArgs  getArg #com.aicodingcli.plugins.CommandArgs  	getOption #com.aicodingcli.plugins.CommandArgs  getOptionOrDefault #com.aicodingcli.plugins.CommandArgs  	hasOption #com.aicodingcli.plugins.CommandArgs  defaultValue %com.aicodingcli.plugins.CommandOption  description %com.aicodingcli.plugins.CommandOption  hasValue %com.aicodingcli.plugins.CommandOption  name %com.aicodingcli.plugins.CommandOption  required %com.aicodingcli.plugins.CommandOption  	shortName %com.aicodingcli.plugins.CommandOption  
getCommand %com.aicodingcli.plugins.CommandPlugin  
hasCommand %com.aicodingcli.plugins.CommandPlugin  CommandArgs )com.aicodingcli.plugins.CommandPluginTest  
CommandOption )com.aicodingcli.plugins.CommandPluginTest  
CommandResult )com.aicodingcli.plugins.CommandPluginTest  PluginTestFramework )com.aicodingcli.plugins.CommandPluginTest  RuntimeException )com.aicodingcli.plugins.CommandPluginTest  TestCommandPlugin )com.aicodingcli.plugins.CommandPluginTest  arrayOf )com.aicodingcli.plugins.CommandPluginTest  assertEquals )com.aicodingcli.plugins.CommandPluginTest  assertFalse )com.aicodingcli.plugins.CommandPluginTest  
assertNotNull )com.aicodingcli.plugins.CommandPluginTest  
assertNull )com.aicodingcli.plugins.CommandPluginTest  
assertTrue )com.aicodingcli.plugins.CommandPluginTest  error )com.aicodingcli.plugins.CommandPluginTest  failure )com.aicodingcli.plugins.CommandPluginTest  listOf )com.aicodingcli.plugins.CommandPluginTest  map )com.aicodingcli.plugins.CommandPluginTest  mapOf )com.aicodingcli.plugins.CommandPluginTest  runBlocking )com.aicodingcli.plugins.CommandPluginTest  success )com.aicodingcli.plugins.CommandPluginTest  
testFramework )com.aicodingcli.plugins.CommandPluginTest  
testPlugin )com.aicodingcli.plugins.CommandPluginTest  to )com.aicodingcli.plugins.CommandPluginTest  	Companion %com.aicodingcli.plugins.CommandResult  data %com.aicodingcli.plugins.CommandResult  error %com.aicodingcli.plugins.CommandResult  exitCode %com.aicodingcli.plugins.CommandResult  failure %com.aicodingcli.plugins.CommandResult  message %com.aicodingcli.plugins.CommandResult  success %com.aicodingcli.plugins.CommandResult  error /com.aicodingcli.plugins.CommandResult.Companion  failure /com.aicodingcli.plugins.CommandResult.Companion  success /com.aicodingcli.plugins.CommandResult.Companion  metadata com.aicodingcli.plugins.Plugin  description %com.aicodingcli.plugins.PluginCommand  name %com.aicodingcli.plugins.PluginCommand  id (com.aicodingcli.plugins.PluginDependency  optional (com.aicodingcli.plugins.PluginDependency  version (com.aicodingcli.plugins.PluginDependency  discoverPlugins .com.aicodingcli.plugins.PluginDiscoveryService  
getPluginInfo .com.aicodingcli.plugins.PluginDiscoveryService  
dispatchEvent -com.aicodingcli.plugins.PluginEventDispatcher  getHandlers -com.aicodingcli.plugins.PluginEventDispatcher  COMMAND_EXECUTED 'com.aicodingcli.plugins.PluginEventType  
PLUGIN_LOADED 'com.aicodingcli.plugins.PluginEventType  cause 0com.aicodingcli.plugins.PluginExecutionException  message 0com.aicodingcli.plugins.PluginExecutionException  filePath "com.aicodingcli.plugins.PluginInfo  fileSize "com.aicodingcli.plugins.PluginInfo  lastModified "com.aicodingcli.plugins.PluginInfo  metadata "com.aicodingcli.plugins.PluginInfo  cause +com.aicodingcli.plugins.PluginLoadException  message +com.aicodingcli.plugins.PluginLoadException  getEventDispatcher %com.aicodingcli.plugins.PluginManager  getLoadedPlugins %com.aicodingcli.plugins.PluginManager  	getPlugin %com.aicodingcli.plugins.PluginManager  getPluginState %com.aicodingcli.plugins.PluginManager  getRegistry %com.aicodingcli.plugins.PluginManager  uninstallPlugin %com.aicodingcli.plugins.PluginManager  unloadPlugin %com.aicodingcli.plugins.PluginManager  updatePlugin %com.aicodingcli.plugins.PluginManager  validatePlugin %com.aicodingcli.plugins.PluginManager  AiServiceFactory )com.aicodingcli.plugins.PluginManagerTest  
ConfigManager )com.aicodingcli.plugins.PluginManagerTest  File )com.aicodingcli.plugins.PluginManagerTest  HistoryManager )com.aicodingcli.plugins.PluginManagerTest  NotImplementedError )com.aicodingcli.plugins.PluginManagerTest  PluginDiscoveryService )com.aicodingcli.plugins.PluginManagerTest  PluginEvent )com.aicodingcli.plugins.PluginManagerTest  PluginEventType )com.aicodingcli.plugins.PluginManagerTest  PluginExecutionException )com.aicodingcli.plugins.PluginManagerTest  
PluginInfo )com.aicodingcli.plugins.PluginManagerTest  PluginLoadException )com.aicodingcli.plugins.PluginManagerTest  
PluginManager )com.aicodingcli.plugins.PluginManagerTest  PluginMetadata )com.aicodingcli.plugins.PluginManagerTest  PluginRegistryStatistics )com.aicodingcli.plugins.PluginManagerTest  PluginValidationResult )com.aicodingcli.plugins.PluginManagerTest  System )com.aicodingcli.plugins.PluginManagerTest  any )com.aicodingcli.plugins.PluginManagerTest  assertEquals )com.aicodingcli.plugins.PluginManagerTest  assertFalse )com.aicodingcli.plugins.PluginManagerTest  
assertNotNull )com.aicodingcli.plugins.PluginManagerTest  
assertNull )com.aicodingcli.plugins.PluginManagerTest  assertThrows )com.aicodingcli.plugins.PluginManagerTest  
assertTrue )com.aicodingcli.plugins.PluginManagerTest  com )com.aicodingcli.plugins.PluginManagerTest  
configManager )com.aicodingcli.plugins.PluginManagerTest  contains )com.aicodingcli.plugins.PluginManagerTest  	emptyList )com.aicodingcli.plugins.PluginManagerTest  historyManager )com.aicodingcli.plugins.PluginManagerTest  java )com.aicodingcli.plugins.PluginManagerTest  listOf )com.aicodingcli.plugins.PluginManagerTest  mapOf )com.aicodingcli.plugins.PluginManagerTest  
pluginManager )com.aicodingcli.plugins.PluginManagerTest  runBlocking )com.aicodingcli.plugins.PluginManagerTest  tempDir )com.aicodingcli.plugins.PluginManagerTest  to )com.aicodingcli.plugins.PluginManagerTest  	writeText )com.aicodingcli.plugins.PluginManagerTest  author &com.aicodingcli.plugins.PluginMetadata  dependencies &com.aicodingcli.plugins.PluginMetadata  description &com.aicodingcli.plugins.PluginMetadata  id &com.aicodingcli.plugins.PluginMetadata  	mainClass &com.aicodingcli.plugins.PluginMetadata  name &com.aicodingcli.plugins.PluginMetadata  permissions &com.aicodingcli.plugins.PluginMetadata  version &com.aicodingcli.plugins.PluginMetadata  	FILE_READ +com.aicodingcli.plugins.PluginOperationType  
FILE_WRITE +com.aicodingcli.plugins.PluginOperationType  ConfigPermission (com.aicodingcli.plugins.PluginPermission  FileSystemPermission (com.aicodingcli.plugins.PluginPermission  HistoryPermission (com.aicodingcli.plugins.PluginPermission  NetworkPermission (com.aicodingcli.plugins.PluginPermission  SystemPermission (com.aicodingcli.plugins.PluginPermission  allowedPaths =com.aicodingcli.plugins.PluginPermission.FileSystemPermission  readOnly =com.aicodingcli.plugins.PluginPermission.FileSystemPermission  allowedHosts :com.aicodingcli.plugins.PluginPermission.NetworkPermission  clear &com.aicodingcli.plugins.PluginRegistry  getAiServicePlugins &com.aicodingcli.plugins.PluginRegistry  getAllCommands &com.aicodingcli.plugins.PluginRegistry  
getAllPlugins &com.aicodingcli.plugins.PluginRegistry  
getCommand &com.aicodingcli.plugins.PluginRegistry  getCommandPlugin &com.aicodingcli.plugins.PluginRegistry  getCommandPlugins &com.aicodingcli.plugins.PluginRegistry  	getPlugin &com.aicodingcli.plugins.PluginRegistry  
getStatistics &com.aicodingcli.plugins.PluginRegistry  
hasCommand &com.aicodingcli.plugins.PluginRegistry  aiServicePlugins 0com.aicodingcli.plugins.PluginRegistryStatistics  commandPlugins 0com.aicodingcli.plugins.PluginRegistryStatistics  supportedAiProviders 0com.aicodingcli.plugins.PluginRegistryStatistics  
totalCommands 0com.aicodingcli.plugins.PluginRegistryStatistics  totalPlugins 0com.aicodingcli.plugins.PluginRegistryStatistics  checkCommandExecution %com.aicodingcli.plugins.PluginSandbox  checkConfigAccess %com.aicodingcli.plugins.PluginSandbox  checkFileAccess %com.aicodingcli.plugins.PluginSandbox  checkHistoryAccess %com.aicodingcli.plugins.PluginSandbox  checkNetworkAccess %com.aicodingcli.plugins.PluginSandbox  getInfo %com.aicodingcli.plugins.PluginSandbox  pluginId %com.aicodingcli.plugins.PluginSandbox  allowedCommands )com.aicodingcli.plugins.PluginSandboxInfo  allowedNetworkHosts )com.aicodingcli.plugins.PluginSandboxInfo  allowedPaths )com.aicodingcli.plugins.PluginSandboxInfo  hasConfigAccess )com.aicodingcli.plugins.PluginSandboxInfo  hasHistoryAccess )com.aicodingcli.plugins.PluginSandboxInfo  pluginId )com.aicodingcli.plugins.PluginSandboxInfo  message /com.aicodingcli.plugins.PluginSecurityException  	operation /com.aicodingcli.plugins.PluginSecurityException  pluginId /com.aicodingcli.plugins.PluginSecurityException  
createSandbox -com.aicodingcli.plugins.PluginSecurityManager  
getSandbox -com.aicodingcli.plugins.PluginSecurityManager  
removeSandbox -com.aicodingcli.plugins.PluginSecurityManager  validatePermissions -com.aicodingcli.plugins.PluginSecurityManager  createDefaultPolicy ,com.aicodingcli.plugins.PluginSecurityPolicy  validatePermissionRequest ,com.aicodingcli.plugins.PluginSecurityPolicy  File *com.aicodingcli.plugins.PluginSecurityTest  PluginMetadata *com.aicodingcli.plugins.PluginSecurityTest  PluginOperation *com.aicodingcli.plugins.PluginSecurityTest  PluginOperationType *com.aicodingcli.plugins.PluginSecurityTest  PluginPermission *com.aicodingcli.plugins.PluginSecurityTest  PluginSecurityException *com.aicodingcli.plugins.PluginSecurityTest  PluginSecurityManager *com.aicodingcli.plugins.PluginSecurityTest  PluginSecurityPolicy *com.aicodingcli.plugins.PluginSecurityTest  any *com.aicodingcli.plugins.PluginSecurityTest  assertEquals *com.aicodingcli.plugins.PluginSecurityTest  assertFalse *com.aicodingcli.plugins.PluginSecurityTest  
assertNotNull *com.aicodingcli.plugins.PluginSecurityTest  
assertNull *com.aicodingcli.plugins.PluginSecurityTest  
assertTrue *com.aicodingcli.plugins.PluginSecurityTest  contains *com.aicodingcli.plugins.PluginSecurityTest  createDefaultPolicy *com.aicodingcli.plugins.PluginSecurityTest  createRestrictedTestPlugin *com.aicodingcli.plugins.PluginSecurityTest  createTestPlugin *com.aicodingcli.plugins.PluginSecurityTest  #createTestPluginWithCommandWildcard *com.aicodingcli.plugins.PluginSecurityTest  #createTestPluginWithNetworkWildcard *com.aicodingcli.plugins.PluginSecurityTest  createTestPluginWithPath *com.aicodingcli.plugins.PluginSecurityTest  	emptyList *com.aicodingcli.plugins.PluginSecurityTest  filterIsInstance *com.aicodingcli.plugins.PluginSecurityTest  first *com.aicodingcli.plugins.PluginSecurityTest  
isNotEmpty *com.aicodingcli.plugins.PluginSecurityTest  listOf *com.aicodingcli.plugins.PluginSecurityTest  securityManager *com.aicodingcli.plugins.PluginSecurityTest  tempDir *com.aicodingcli.plugins.PluginSecurityTest  
testPlugin *com.aicodingcli.plugins.PluginSecurityTest  validatePermissionRequest *com.aicodingcli.plugins.PluginSecurityTest  	writeText *com.aicodingcli.plugins.PluginSecurityTest  ERROR #com.aicodingcli.plugins.PluginState  INITIALIZED #com.aicodingcli.plugins.PluginState  LOADED #com.aicodingcli.plugins.PluginState  RUNNING #com.aicodingcli.plugins.PluginState  STOPPED #com.aicodingcli.plugins.PluginState  UNLOADED #com.aicodingcli.plugins.PluginState  values #com.aicodingcli.plugins.PluginState  PluginDependency "com.aicodingcli.plugins.PluginTest  PluginExecutionException "com.aicodingcli.plugins.PluginTest  PluginLoadException "com.aicodingcli.plugins.PluginTest  PluginMetadata "com.aicodingcli.plugins.PluginTest  PluginPermission "com.aicodingcli.plugins.PluginTest  PluginState "com.aicodingcli.plugins.PluginTest  PluginTestFramework "com.aicodingcli.plugins.PluginTest  PluginValidationResult "com.aicodingcli.plugins.PluginTest  RuntimeException "com.aicodingcli.plugins.PluginTest  assertEquals "com.aicodingcli.plugins.PluginTest  assertFalse "com.aicodingcli.plugins.PluginTest  
assertNotNull "com.aicodingcli.plugins.PluginTest  
assertNull "com.aicodingcli.plugins.PluginTest  
assertTrue "com.aicodingcli.plugins.PluginTest  contains "com.aicodingcli.plugins.PluginTest  	emptyList "com.aicodingcli.plugins.PluginTest  listOf "com.aicodingcli.plugins.PluginTest  
testFramework "com.aicodingcli.plugins.PluginTest  createTestContext +com.aicodingcli.plugins.PluginTestFramework  simulateCommand +com.aicodingcli.plugins.PluginTestFramework  testPluginValidation +com.aicodingcli.plugins.PluginTestFramework  errors (com.aicodingcli.plugins.PluginTestResult  success (com.aicodingcli.plugins.PluginTestResult  errors .com.aicodingcli.plugins.PluginValidationResult  isValid .com.aicodingcli.plugins.PluginValidationResult  warnings .com.aicodingcli.plugins.PluginValidationResult  
AiResponse %com.aicodingcli.plugins.TestAiService  
AiStreamChunk %com.aicodingcli.plugins.TestAiService  FinishReason %com.aicodingcli.plugins.TestAiService  
TokenUsage %com.aicodingcli.plugins.TestAiService  kotlinx %com.aicodingcli.plugins.TestAiService  last %com.aicodingcli.plugins.TestAiService  validateRequest %com.aicodingcli.plugins.TestAiService  
AiProvider +com.aicodingcli.plugins.TestAiServicePlugin  PluginMetadata +com.aicodingcli.plugins.TestAiServicePlugin  
TestAiService +com.aicodingcli.plugins.TestAiServicePlugin  createAiService +com.aicodingcli.plugins.TestAiServicePlugin  getDefaultConfig +com.aicodingcli.plugins.TestAiServicePlugin  getSupportedModels +com.aicodingcli.plugins.TestAiServicePlugin  
initialize +com.aicodingcli.plugins.TestAiServicePlugin  listOf +com.aicodingcli.plugins.TestAiServicePlugin  metadata +com.aicodingcli.plugins.TestAiServicePlugin  shutdown +com.aicodingcli.plugins.TestAiServicePlugin  supportedProvider +com.aicodingcli.plugins.TestAiServicePlugin  validateConfig +com.aicodingcli.plugins.TestAiServicePlugin  validateProviderConfig +com.aicodingcli.plugins.TestAiServicePlugin  
CommandOption )com.aicodingcli.plugins.TestCommandPlugin  
CommandResult )com.aicodingcli.plugins.TestCommandPlugin  PluginMetadata )com.aicodingcli.plugins.TestCommandPlugin  commands )com.aicodingcli.plugins.TestCommandPlugin  
createCommand )com.aicodingcli.plugins.TestCommandPlugin  
getCommand )com.aicodingcli.plugins.TestCommandPlugin  
hasCommand )com.aicodingcli.plugins.TestCommandPlugin  
initialize )com.aicodingcli.plugins.TestCommandPlugin  joinToString )com.aicodingcli.plugins.TestCommandPlugin  listOf )com.aicodingcli.plugins.TestCommandPlugin  metadata )com.aicodingcli.plugins.TestCommandPlugin  shutdown )com.aicodingcli.plugins.TestCommandPlugin  success )com.aicodingcli.plugins.TestCommandPlugin  	uppercase )com.aicodingcli.plugins.TestCommandPlugin  aiServiceFactory )com.aicodingcli.plugins.TestPluginContext  
configManager )com.aicodingcli.plugins.TestPluginContext  getRegisteredCommands )com.aicodingcli.plugins.TestPluginContext  
getSharedData )com.aicodingcli.plugins.TestPluginContext  
hasPermission )com.aicodingcli.plugins.TestPluginContext  historyManager )com.aicodingcli.plugins.TestPluginContext  logger )com.aicodingcli.plugins.TestPluginContext  
setSharedData )com.aicodingcli.plugins.TestPluginContext  
coroutines com.aicodingcli.plugins.kotlinx  flow *com.aicodingcli.plugins.kotlinx.coroutines  Flow /com.aicodingcli.plugins.kotlinx.coroutines.flow  AiHttpClient io.ktor.client.engine.mock  ByteReadChannel io.ktor.client.engine.mock  	Exception io.ktor.client.engine.mock  
HttpException io.ktor.client.engine.mock  HttpHeaders io.ktor.client.engine.mock  
HttpMethod io.ktor.client.engine.mock  HttpStatusCode io.ktor.client.engine.mock  
MockEngine io.ktor.client.engine.mock  MockRequestHandleScope io.ktor.client.engine.mock  RetryConfig io.ktor.client.engine.mock  Test io.ktor.client.engine.mock  assertEquals io.ktor.client.engine.mock  assertThrows io.ktor.client.engine.mock  	headersOf io.ktor.client.engine.mock  kotlinx io.ktor.client.engine.mock  mapOf io.ktor.client.engine.mock  respond io.ktor.client.engine.mock  runTest io.ktor.client.engine.mock  to io.ktor.client.engine.mock  	Companion %io.ktor.client.engine.mock.MockEngine  invoke %io.ktor.client.engine.mock.MockEngine  invoke /io.ktor.client.engine.mock.MockEngine.Companion  ByteReadChannel 1io.ktor.client.engine.mock.MockRequestHandleScope  	Exception 1io.ktor.client.engine.mock.MockRequestHandleScope  HttpHeaders 1io.ktor.client.engine.mock.MockRequestHandleScope  
HttpMethod 1io.ktor.client.engine.mock.MockRequestHandleScope  HttpStatusCode 1io.ktor.client.engine.mock.MockRequestHandleScope  assertEquals 1io.ktor.client.engine.mock.MockRequestHandleScope  	headersOf 1io.ktor.client.engine.mock.MockRequestHandleScope  kotlinx 1io.ktor.client.engine.mock.MockRequestHandleScope  respond 1io.ktor.client.engine.mock.MockRequestHandleScope  HttpRequestData io.ktor.client.request  HttpResponseData io.ktor.client.request  body &io.ktor.client.request.HttpRequestData  headers &io.ktor.client.request.HttpRequestData  method &io.ktor.client.request.HttpRequestData  AiHttpClient io.ktor.http  	AiMessage io.ktor.http  
AiProvider io.ktor.http  	AiRequest io.ktor.http  AiServiceConfig io.ktor.http  
BeforeEach io.ktor.http  ByteReadChannel io.ktor.http  ClaudeException io.ktor.http  
ClaudeService io.ktor.http  ContentType io.ktor.http  	Exception io.ktor.http  FinishReason io.ktor.http  Headers io.ktor.http  
HttpException io.ktor.http  HttpHeaders io.ktor.http  
HttpMethod io.ktor.http  HttpResponse io.ktor.http  HttpStatusCode io.ktor.http  IllegalArgumentException io.ktor.http  MessageRole io.ktor.http  
MockEngine io.ktor.http  OllamaException io.ktor.http  
OllamaService io.ktor.http  OpenAiException io.ktor.http  
OpenAiService io.ktor.http  RetryConfig io.ktor.http  Test io.ktor.http  assertEquals io.ktor.http  assertFalse io.ktor.http  
assertNotNull io.ktor.http  assertThrows io.ktor.http  
assertTrue io.ktor.http  
claudeService io.ktor.http  coEvery io.ktor.http  coVerify io.ktor.http  com io.ktor.http  config io.ktor.http  contains io.ktor.http  	emptyList io.ktor.http  	headersOf io.ktor.http  kotlinx io.ktor.http  listOf io.ktor.http  mapOf io.ktor.http  mockHttpClient io.ktor.http  mockk io.ktor.http  
ollamaService io.ktor.http  
openAiService io.ktor.http  respond io.ktor.http  runTest io.ktor.http  to io.ktor.http  
trimIndent io.ktor.http  toString io.ktor.http.ContentType  toString &io.ktor.http.HeaderValueWithParameters  get io.ktor.http.Headers  ContentType io.ktor.http.HttpHeaders  	Companion io.ktor.http.HttpMethod  Post io.ktor.http.HttpMethod  Post !io.ktor.http.HttpMethod.Companion  	Companion io.ktor.http.HttpStatusCode  Created io.ktor.http.HttpStatusCode  NotFound io.ktor.http.HttpStatusCode  OK io.ktor.http.HttpStatusCode  ServiceUnavailable io.ktor.http.HttpStatusCode  TooManyRequests io.ktor.http.HttpStatusCode  Unauthorized io.ktor.http.HttpStatusCode  Created %io.ktor.http.HttpStatusCode.Companion  NotFound %io.ktor.http.HttpStatusCode.Companion  OK %io.ktor.http.HttpStatusCode.Companion  ServiceUnavailable %io.ktor.http.HttpStatusCode.Companion  TooManyRequests %io.ktor.http.HttpStatusCode.Companion  Unauthorized %io.ktor.http.HttpStatusCode.Companion  OutgoingContent io.ktor.http.content  contentType $io.ktor.http.content.OutgoingContent  get io.ktor.util.StringValues  AiHttpClient io.ktor.utils.io  ByteReadChannel io.ktor.utils.io  	Exception io.ktor.utils.io  
HttpException io.ktor.utils.io  HttpHeaders io.ktor.utils.io  
HttpMethod io.ktor.utils.io  HttpStatusCode io.ktor.utils.io  
MockEngine io.ktor.utils.io  RetryConfig io.ktor.utils.io  Test io.ktor.utils.io  assertEquals io.ktor.utils.io  assertThrows io.ktor.utils.io  	headersOf io.ktor.utils.io  kotlinx io.ktor.utils.io  mapOf io.ktor.utils.io  respond io.ktor.utils.io  runTest io.ktor.utils.io  to io.ktor.utils.io  AiHttpClient io.mockk  	AiMessage io.mockk  
AiProvider io.mockk  	AiRequest io.mockk  AiServiceConfig io.mockk  
BeforeEach io.mockk  ClaudeException io.mockk  
ClaudeService io.mockk  FinishReason io.mockk  HttpResponse io.mockk  HttpStatusCode io.mockk  IllegalArgumentException io.mockk  MessageRole io.mockk  MockKAdditionalAnswerScope io.mockk  MockKMatcherScope io.mockk  MockKStubScope io.mockk  MockKVerificationScope io.mockk  OllamaException io.mockk  
OllamaService io.mockk  OpenAiException io.mockk  
OpenAiService io.mockk  Test io.mockk  assertEquals io.mockk  assertFalse io.mockk  
assertNotNull io.mockk  assertThrows io.mockk  
assertTrue io.mockk  
claudeService io.mockk  coEvery io.mockk  coVerify io.mockk  com io.mockk  config io.mockk  contains io.mockk  	emptyList io.mockk  listOf io.mockk  mapOf io.mockk  mockHttpClient io.mockk  mockk io.mockk  
ollamaService io.mockk  
openAiService io.mockk  runTest io.mockk  to io.mockk  
trimIndent io.mockk  any io.mockk.MockKMatcherScope  match io.mockk.MockKMatcherScope  mockHttpClient io.mockk.MockKMatcherScope  returns io.mockk.MockKStubScope  throws io.mockk.MockKStubScope  any io.mockk.MockKVerificationScope  contains io.mockk.MockKVerificationScope  match io.mockk.MockKVerificationScope  mockHttpClient io.mockk.MockKVerificationScope  ByteArrayOutputStream java.io  File java.io  PrintStream java.io  toString java.io.ByteArrayOutputStream  absolutePath java.io.File  deleteRecursively java.io.File  exists java.io.File  	writeText java.io.File  Class 	java.lang  	Exception 	java.lang  IllegalArgumentException 	java.lang  RuntimeException 	java.lang  SecurityException 	java.lang  currentTimeMillis java.lang.System  out java.lang.System  setOut java.lang.System  sleep java.lang.Thread  Files 
java.nio.file  Path 
java.nio.file  createTempDirectory java.nio.file.Files  toFile java.nio.file.Path  toString java.nio.file.Path  Instant 	java.time  epochSecond java.time.Instant  now java.time.Instant  Array kotlin  CharSequence kotlin  	Exception kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  IllegalArgumentException kotlin  NotImplementedError kotlin  Nothing kotlin  Pair kotlin  Result kotlin  	Throwable kotlin  arrayOf kotlin  map kotlin  repeat kotlin  to kotlin  toString 
kotlin.Any  contains kotlin.Array  isEmpty kotlin.CharSequence  	compareTo 
kotlin.Double  rangeTo 
kotlin.Double  message kotlin.Exception  printStackTrace kotlin.Exception  message kotlin.IllegalArgumentException  	compareTo 
kotlin.Int  inc 
kotlin.Int  plus 
kotlin.Int  	compareTo kotlin.Long  minus kotlin.Long  contains 
kotlin.String  isBlank 
kotlin.String  isEmpty 
kotlin.String  length 
kotlin.String  lines 
kotlin.String  	lowercase 
kotlin.String  
startsWith 
kotlin.String  take 
kotlin.String  to 
kotlin.String  trim 
kotlin.String  
trimIndent 
kotlin.String  	uppercase 
kotlin.String  cause kotlin.Throwable  message kotlin.Throwable  printStackTrace kotlin.Throwable  List kotlin.collections  Map kotlin.collections  MutableList kotlin.collections  all kotlin.collections  any kotlin.collections  contains kotlin.collections  	emptyList kotlin.collections  emptyMap kotlin.collections  filter kotlin.collections  filterIsInstance kotlin.collections  first kotlin.collections  forEach kotlin.collections  forEachIndexed kotlin.collections  isEmpty kotlin.collections  
isNotEmpty kotlin.collections  joinToString kotlin.collections  last kotlin.collections  listOf kotlin.collections  map kotlin.collections  mapOf kotlin.collections  
mutableListOf kotlin.collections  take kotlin.collections  all kotlin.collections.List  any kotlin.collections.List  contains kotlin.collections.List  filter kotlin.collections.List  filterIsInstance kotlin.collections.List  first kotlin.collections.List  forEachIndexed kotlin.collections.List  get kotlin.collections.List  isEmpty kotlin.collections.List  
isNotEmpty kotlin.collections.List  joinToString kotlin.collections.List  last kotlin.collections.List  map kotlin.collections.List  size kotlin.collections.List  containsKey kotlin.collections.Map  get kotlin.collections.Map  isEmpty kotlin.collections.Map  
isNotEmpty kotlin.collections.Map  size kotlin.collections.Map  add kotlin.collections.MutableList  get kotlin.collections.MutableList  isEmpty kotlin.collections.MutableList  size kotlin.collections.MutableList  SuspendFunction1 kotlin.coroutines  SuspendFunction2 kotlin.coroutines  createTempFile 	kotlin.io  deleteRecursively 	kotlin.io  println 	kotlin.io  
startsWith 	kotlin.io  	writeText 	kotlin.io  java 
kotlin.jvm  ClosedFloatingPointRange 
kotlin.ranges  ClosedRange 
kotlin.ranges  contains 
kotlin.ranges  first 
kotlin.ranges  last 
kotlin.ranges  rangeTo 
kotlin.ranges  contains &kotlin.ranges.ClosedFloatingPointRange  java kotlin.reflect.KClass  Sequence kotlin.sequences  all kotlin.sequences  any kotlin.sequences  contains kotlin.sequences  filter kotlin.sequences  filterIsInstance kotlin.sequences  first kotlin.sequences  forEach kotlin.sequences  forEachIndexed kotlin.sequences  joinToString kotlin.sequences  last kotlin.sequences  map kotlin.sequences  take kotlin.sequences  all kotlin.text  any kotlin.text  contains kotlin.text  filter kotlin.text  first kotlin.text  forEach kotlin.text  forEachIndexed kotlin.text  isBlank kotlin.text  isEmpty kotlin.text  
isNotEmpty kotlin.text  last kotlin.text  lines kotlin.text  	lowercase kotlin.text  map kotlin.text  repeat kotlin.text  
startsWith kotlin.text  take kotlin.text  trim kotlin.text  
trimIndent kotlin.text  	uppercase kotlin.text  CoroutineScope kotlinx.coroutines  delay kotlinx.coroutines  runBlocking kotlinx.coroutines  	AiMessage !kotlinx.coroutines.CoroutineScope  
AiProvider !kotlinx.coroutines.CoroutineScope  	AiRequest !kotlinx.coroutines.CoroutineScope  AiServiceConfig !kotlinx.coroutines.CoroutineScope  File !kotlinx.coroutines.CoroutineScope  FinishReason !kotlinx.coroutines.CoroutineScope  ImprovementType !kotlinx.coroutines.CoroutineScope  	IssueType !kotlinx.coroutines.CoroutineScope  MessageRole !kotlinx.coroutines.CoroutineScope  NotImplementedError !kotlinx.coroutines.CoroutineScope  PluginEvent !kotlinx.coroutines.CoroutineScope  PluginEventType !kotlinx.coroutines.CoroutineScope  ProgrammingLanguage !kotlinx.coroutines.CoroutineScope  analyzer !kotlinx.coroutines.CoroutineScope  any !kotlinx.coroutines.CoroutineScope  arrayOf !kotlinx.coroutines.CoroutineScope  assertEquals !kotlinx.coroutines.CoroutineScope  assertFalse !kotlinx.coroutines.CoroutineScope  
assertNotNull !kotlinx.coroutines.CoroutineScope  assertThrows !kotlinx.coroutines.CoroutineScope  
assertTrue !kotlinx.coroutines.CoroutineScope  createTempFile !kotlinx.coroutines.CoroutineScope  
isNotEmpty !kotlinx.coroutines.CoroutineScope  java !kotlinx.coroutines.CoroutineScope  listOf !kotlinx.coroutines.CoroutineScope  mapOf !kotlinx.coroutines.CoroutineScope  
pluginManager !kotlinx.coroutines.CoroutineScope  println !kotlinx.coroutines.CoroutineScope  runBlocking !kotlinx.coroutines.CoroutineScope  tempDir !kotlinx.coroutines.CoroutineScope  
testFramework !kotlinx.coroutines.CoroutineScope  
testPlugin !kotlinx.coroutines.CoroutineScope  to !kotlinx.coroutines.CoroutineScope  toList !kotlinx.coroutines.CoroutineScope  
trimIndent !kotlinx.coroutines.CoroutineScope  	writeText !kotlinx.coroutines.CoroutineScope  Flow kotlinx.coroutines.flow  
FlowCollector kotlinx.coroutines.flow  flowOf kotlinx.coroutines.flow  toList kotlinx.coroutines.flow  collect kotlinx.coroutines.flow.Flow  toList kotlinx.coroutines.flow.Flow  <SAM-CONSTRUCTOR> %kotlinx.coroutines.flow.FlowCollector  	TestScope kotlinx.coroutines.test  runTest kotlinx.coroutines.test  AiHttpClient !kotlinx.coroutines.test.TestScope  	AiMessage !kotlinx.coroutines.test.TestScope  
AiProvider !kotlinx.coroutines.test.TestScope  	AiRequest !kotlinx.coroutines.test.TestScope  AiServiceConfig !kotlinx.coroutines.test.TestScope  AiServiceFactory !kotlinx.coroutines.test.TestScope  
AiStreamChunk !kotlinx.coroutines.test.TestScope  	AppConfig !kotlinx.coroutines.test.TestScope  ByteReadChannel !kotlinx.coroutines.test.TestScope  
ClaudeService !kotlinx.coroutines.test.TestScope  	Exception !kotlinx.coroutines.test.TestScope  File !kotlinx.coroutines.test.TestScope  FinishReason !kotlinx.coroutines.test.TestScope  HttpHeaders !kotlinx.coroutines.test.TestScope  
HttpMethod !kotlinx.coroutines.test.TestScope  HttpResponse !kotlinx.coroutines.test.TestScope  HttpStatusCode !kotlinx.coroutines.test.TestScope  MessageRole !kotlinx.coroutines.test.TestScope  
MockEngine !kotlinx.coroutines.test.TestScope  
OllamaService !kotlinx.coroutines.test.TestScope  
OpenAiService !kotlinx.coroutines.test.TestScope  RetryConfig !kotlinx.coroutines.test.TestScope  assertDoesNotThrow !kotlinx.coroutines.test.TestScope  assertEquals !kotlinx.coroutines.test.TestScope  assertFalse !kotlinx.coroutines.test.TestScope  
assertNotNull !kotlinx.coroutines.test.TestScope  assertThrows !kotlinx.coroutines.test.TestScope  
assertTrue !kotlinx.coroutines.test.TestScope  
claudeService !kotlinx.coroutines.test.TestScope  coEvery !kotlinx.coroutines.test.TestScope  coVerify !kotlinx.coroutines.test.TestScope  com !kotlinx.coroutines.test.TestScope  config !kotlinx.coroutines.test.TestScope  
configManager !kotlinx.coroutines.test.TestScope  contains !kotlinx.coroutines.test.TestScope  
createService !kotlinx.coroutines.test.TestScope  	emptyList !kotlinx.coroutines.test.TestScope  	headersOf !kotlinx.coroutines.test.TestScope  
isNotEmpty !kotlinx.coroutines.test.TestScope  kotlinx !kotlinx.coroutines.test.TestScope  listOf !kotlinx.coroutines.test.TestScope  mapOf !kotlinx.coroutines.test.TestScope  mockHttpClient !kotlinx.coroutines.test.TestScope  mockk !kotlinx.coroutines.test.TestScope  
mutableListOf !kotlinx.coroutines.test.TestScope  
ollamaService !kotlinx.coroutines.test.TestScope  
openAiService !kotlinx.coroutines.test.TestScope  respond !kotlinx.coroutines.test.TestScope  tempDir !kotlinx.coroutines.test.TestScope  to !kotlinx.coroutines.test.TestScope  
trimIndent !kotlinx.coroutines.test.TestScope  	writeText !kotlinx.coroutines.test.TestScope  	AfterEach org.junit.jupiter.api  AiHttpClient org.junit.jupiter.api  	AiMessage org.junit.jupiter.api  
AiProvider org.junit.jupiter.api  	AiRequest org.junit.jupiter.api  
AiResponse org.junit.jupiter.api  	AiService org.junit.jupiter.api  AiServiceConfig org.junit.jupiter.api  AiServiceFactory org.junit.jupiter.api  AiServicePluginRegistry org.junit.jupiter.api  
AiStreamChunk org.junit.jupiter.api  	AppConfig org.junit.jupiter.api  
BaseAiService org.junit.jupiter.api  BaseAiServicePlugin org.junit.jupiter.api  BaseCommandPlugin org.junit.jupiter.api  
BeforeEach org.junit.jupiter.api  Boolean org.junit.jupiter.api  ByteReadChannel org.junit.jupiter.api  ClaudeException org.junit.jupiter.api  
ClaudeService org.junit.jupiter.api  CommandArgs org.junit.jupiter.api  
CommandOption org.junit.jupiter.api  
CommandResult org.junit.jupiter.api  
ConfigManager org.junit.jupiter.api  ConversationMessage org.junit.jupiter.api  ConversationSession org.junit.jupiter.api  	Exception org.junit.jupiter.api  File org.junit.jupiter.api  Files org.junit.jupiter.api  FinishReason org.junit.jupiter.api  HistoryManager org.junit.jupiter.api  HistorySearchCriteria org.junit.jupiter.api  HistoryStatistics org.junit.jupiter.api  
HttpException org.junit.jupiter.api  HttpHeaders org.junit.jupiter.api  
HttpMethod org.junit.jupiter.api  HttpResponse org.junit.jupiter.api  HttpStatusCode org.junit.jupiter.api  IllegalArgumentException org.junit.jupiter.api  ImprovementType org.junit.jupiter.api  Instant org.junit.jupiter.api  	IssueType org.junit.jupiter.api  List org.junit.jupiter.api  MessageRole org.junit.jupiter.api  MessageTokenUsage org.junit.jupiter.api  MetricsCalculator org.junit.jupiter.api  
MockEngine org.junit.jupiter.api  NotImplementedError org.junit.jupiter.api  OllamaException org.junit.jupiter.api  
OllamaService org.junit.jupiter.api  OpenAiException org.junit.jupiter.api  
OpenAiService org.junit.jupiter.api  Path org.junit.jupiter.api  Plugin org.junit.jupiter.api  
PluginContext org.junit.jupiter.api  PluginDependency org.junit.jupiter.api  PluginDiscoveryService org.junit.jupiter.api  PluginEvent org.junit.jupiter.api  PluginEventType org.junit.jupiter.api  PluginExecutionException org.junit.jupiter.api  
PluginInfo org.junit.jupiter.api  PluginLoadException org.junit.jupiter.api  
PluginManager org.junit.jupiter.api  PluginMetadata org.junit.jupiter.api  PluginOperation org.junit.jupiter.api  PluginOperationType org.junit.jupiter.api  PluginPermission org.junit.jupiter.api  PluginRegistryStatistics org.junit.jupiter.api  PluginSecurityException org.junit.jupiter.api  PluginSecurityManager org.junit.jupiter.api  PluginSecurityPolicy org.junit.jupiter.api  PluginState org.junit.jupiter.api  PluginTestFramework org.junit.jupiter.api  PluginValidationResult org.junit.jupiter.api  ProgrammingLanguage org.junit.jupiter.api  QualityAnalyzer org.junit.jupiter.api  RetryConfig org.junit.jupiter.api  RuntimeException org.junit.jupiter.api  SecurityException org.junit.jupiter.api  String org.junit.jupiter.api  System org.junit.jupiter.api  TempDir org.junit.jupiter.api  Test org.junit.jupiter.api  
TestAiService org.junit.jupiter.api  TestAiServicePlugin org.junit.jupiter.api  TestCommandPlugin org.junit.jupiter.api  TestPluginContext org.junit.jupiter.api  Thread org.junit.jupiter.api  
TokenUsage org.junit.jupiter.api  all org.junit.jupiter.api  any org.junit.jupiter.api  arrayOf org.junit.jupiter.api  assertDoesNotThrow org.junit.jupiter.api  assertEquals org.junit.jupiter.api  assertFalse org.junit.jupiter.api  
assertNotNull org.junit.jupiter.api  
assertNull org.junit.jupiter.api  assertThrows org.junit.jupiter.api  
assertTrue org.junit.jupiter.api  
claudeService org.junit.jupiter.api  coEvery org.junit.jupiter.api  coVerify org.junit.jupiter.api  com org.junit.jupiter.api  config org.junit.jupiter.api  
configManager org.junit.jupiter.api  contains org.junit.jupiter.api  createDefaultPolicy org.junit.jupiter.api  
createService org.junit.jupiter.api  deleteRecursively org.junit.jupiter.api  	emptyList org.junit.jupiter.api  emptyMap org.junit.jupiter.api  error org.junit.jupiter.api  failure org.junit.jupiter.api  filter org.junit.jupiter.api  filterIsInstance org.junit.jupiter.api  first org.junit.jupiter.api  forEach org.junit.jupiter.api  
getAllPlugins org.junit.jupiter.api  	getPlugin org.junit.jupiter.api  	headersOf org.junit.jupiter.api  isBlank org.junit.jupiter.api  
isNotEmpty org.junit.jupiter.api  isProviderSupported org.junit.jupiter.api  java org.junit.jupiter.api  joinToString org.junit.jupiter.api  kotlinx org.junit.jupiter.api  last org.junit.jupiter.api  lines org.junit.jupiter.api  listOf org.junit.jupiter.api  map org.junit.jupiter.api  mapOf org.junit.jupiter.api  mockHttpClient org.junit.jupiter.api  mockk org.junit.jupiter.api  
mutableListOf org.junit.jupiter.api  
ollamaService org.junit.jupiter.api  
openAiService org.junit.jupiter.api  
pluginManager org.junit.jupiter.api  rangeTo org.junit.jupiter.api  register org.junit.jupiter.api  repeat org.junit.jupiter.api  respond org.junit.jupiter.api  runBlocking org.junit.jupiter.api  runTest org.junit.jupiter.api  success org.junit.jupiter.api  take org.junit.jupiter.api  tempDir org.junit.jupiter.api  
testFramework org.junit.jupiter.api  
testPlugin org.junit.jupiter.api  to org.junit.jupiter.api  toList org.junit.jupiter.api  
trimIndent org.junit.jupiter.api  
unregister org.junit.jupiter.api  	uppercase org.junit.jupiter.api  validatePermissionRequest org.junit.jupiter.api  	writeText org.junit.jupiter.api  assertDoesNotThrow  org.junit.jupiter.api.Assertions  assertEquals  org.junit.jupiter.api.Assertions  assertFalse  org.junit.jupiter.api.Assertions  
assertNotNull  org.junit.jupiter.api.Assertions  
assertNull  org.junit.jupiter.api.Assertions  assertThrows  org.junit.jupiter.api.Assertions  
assertTrue  org.junit.jupiter.api.Assertions  ConfigPermission &org.junit.jupiter.api.PluginPermission  FileSystemPermission &org.junit.jupiter.api.PluginPermission  HistoryPermission &org.junit.jupiter.api.PluginPermission  NetworkPermission &org.junit.jupiter.api.PluginPermission  SystemPermission &org.junit.jupiter.api.PluginPermission  
Executable org.junit.jupiter.api.function  ThrowingSupplier org.junit.jupiter.api.function  <SAM-CONSTRUCTOR> )org.junit.jupiter.api.function.Executable  <SAM-CONSTRUCTOR> /org.junit.jupiter.api.function.ThrowingSupplier  TempDir org.junit.jupiter.api.io  
coroutines org.junit.jupiter.api.kotlinx  flow (org.junit.jupiter.api.kotlinx.coroutines  Flow -org.junit.jupiter.api.kotlinx.coroutines.flow  
BeforeEach com.aicodingcli.conversation  ConversationSession com.aicodingcli.conversation  ConversationState com.aicodingcli.conversation  ConversationStateManager com.aicodingcli.conversation  ConversationStateManagerTest com.aicodingcli.conversation  ConversationStatus com.aicodingcli.conversation  ExecutableTask com.aicodingcli.conversation  ExecutionError com.aicodingcli.conversation  
ExecutionStep com.aicodingcli.conversation  File com.aicodingcli.conversation  IllegalArgumentException com.aicodingcli.conversation  TempDir com.aicodingcli.conversation  Test com.aicodingcli.conversation  assertEquals com.aicodingcli.conversation  
assertNotNull com.aicodingcli.conversation  
assertNull com.aicodingcli.conversation  assertThrows com.aicodingcli.conversation  
assertTrue com.aicodingcli.conversation  conversationStateManager com.aicodingcli.conversation  	emptyList com.aicodingcli.conversation  emptyMap com.aicodingcli.conversation  java com.aicodingcli.conversation  listOf com.aicodingcli.conversation  mapOf com.aicodingcli.conversation  runTest com.aicodingcli.conversation  tempDir com.aicodingcli.conversation  to com.aicodingcli.conversation  	createdAt 0com.aicodingcli.conversation.ConversationSession  executionHistory 0com.aicodingcli.conversation.ConversationSession  id 0com.aicodingcli.conversation.ConversationSession  requirement 0com.aicodingcli.conversation.ConversationSession  state 0com.aicodingcli.conversation.ConversationSession  tasks 0com.aicodingcli.conversation.ConversationSession  	updatedAt 0com.aicodingcli.conversation.ConversationSession  context .com.aicodingcli.conversation.ConversationState  currentTaskIndex .com.aicodingcli.conversation.ConversationState  errors .com.aicodingcli.conversation.ConversationState  executionRound .com.aicodingcli.conversation.ConversationState  status .com.aicodingcli.conversation.ConversationState  
createSession 5com.aicodingcli.conversation.ConversationStateManager  
endSession 5com.aicodingcli.conversation.ConversationStateManager  
getSession 5com.aicodingcli.conversation.ConversationStateManager  updateState 5com.aicodingcli.conversation.ConversationStateManager  ConversationState 9com.aicodingcli.conversation.ConversationStateManagerTest  ConversationStateManager 9com.aicodingcli.conversation.ConversationStateManagerTest  ConversationStatus 9com.aicodingcli.conversation.ConversationStateManagerTest  ExecutionError 9com.aicodingcli.conversation.ConversationStateManagerTest  IllegalArgumentException 9com.aicodingcli.conversation.ConversationStateManagerTest  assertEquals 9com.aicodingcli.conversation.ConversationStateManagerTest  
assertNotNull 9com.aicodingcli.conversation.ConversationStateManagerTest  
assertNull 9com.aicodingcli.conversation.ConversationStateManagerTest  assertThrows 9com.aicodingcli.conversation.ConversationStateManagerTest  
assertTrue 9com.aicodingcli.conversation.ConversationStateManagerTest  conversationStateManager 9com.aicodingcli.conversation.ConversationStateManagerTest  	emptyList 9com.aicodingcli.conversation.ConversationStateManagerTest  emptyMap 9com.aicodingcli.conversation.ConversationStateManagerTest  java 9com.aicodingcli.conversation.ConversationStateManagerTest  listOf 9com.aicodingcli.conversation.ConversationStateManagerTest  mapOf 9com.aicodingcli.conversation.ConversationStateManagerTest  runTest 9com.aicodingcli.conversation.ConversationStateManagerTest  tempDir 9com.aicodingcli.conversation.ConversationStateManagerTest  to 9com.aicodingcli.conversation.ConversationStateManagerTest  	COMPLETED /com.aicodingcli.conversation.ConversationStatus  CREATED /com.aicodingcli.conversation.ConversationStatus  	Companion /com.aicodingcli.conversation.ConversationStatus  	EXECUTING /com.aicodingcli.conversation.ConversationStatus  PLANNING /com.aicodingcli.conversation.ConversationStatus  message +com.aicodingcli.conversation.ExecutionError  isAfter java.time.Instant  ConversationState !kotlinx.coroutines.test.TestScope  ConversationStateManager !kotlinx.coroutines.test.TestScope  ConversationStatus !kotlinx.coroutines.test.TestScope  ExecutionError !kotlinx.coroutines.test.TestScope  IllegalArgumentException !kotlinx.coroutines.test.TestScope  
assertNull !kotlinx.coroutines.test.TestScope  conversationStateManager !kotlinx.coroutines.test.TestScope  emptyMap !kotlinx.coroutines.test.TestScope  java !kotlinx.coroutines.test.TestScope  runTest !kotlinx.coroutines.test.TestScope  ConversationState org.junit.jupiter.api  ConversationStateManager org.junit.jupiter.api  ConversationStatus org.junit.jupiter.api  ExecutionError org.junit.jupiter.api  conversationStateManager org.junit.jupiter.api  runBlocking com.aicodingcli.conversation  runBlocking 9com.aicodingcli.conversation.ConversationStateManagerTest  conversationStateManager !kotlinx.coroutines.CoroutineScope  runBlocking !kotlinx.coroutines.test.TestScope  DefaultTaskDecomposer com.aicodingcli.conversation  ProjectContext com.aicodingcli.conversation  TaskDecomposer com.aicodingcli.conversation  TaskDecomposerTest com.aicodingcli.conversation  ToolCall com.aicodingcli.conversation  ValidationResult com.aicodingcli.conversation  any com.aicodingcli.conversation  assertFalse com.aicodingcli.conversation  assertNotEquals com.aicodingcli.conversation  contains com.aicodingcli.conversation  distinct com.aicodingcli.conversation  find com.aicodingcli.conversation  first com.aicodingcli.conversation  forEach com.aicodingcli.conversation  
isNotEmpty com.aicodingcli.conversation  map com.aicodingcli.conversation  sorted com.aicodingcli.conversation  taskDecomposer com.aicodingcli.conversation  dependencies +com.aicodingcli.conversation.ExecutableTask  description +com.aicodingcli.conversation.ExecutableTask  id +com.aicodingcli.conversation.ExecutableTask  priority +com.aicodingcli.conversation.ExecutableTask  	toolCalls +com.aicodingcli.conversation.ExecutableTask  	decompose +com.aicodingcli.conversation.TaskDecomposer  
refineTask +com.aicodingcli.conversation.TaskDecomposer  validateTaskSequence +com.aicodingcli.conversation.TaskDecomposer  DefaultTaskDecomposer /com.aicodingcli.conversation.TaskDecomposerTest  ExecutableTask /com.aicodingcli.conversation.TaskDecomposerTest  ProjectContext /com.aicodingcli.conversation.TaskDecomposerTest  ToolCall /com.aicodingcli.conversation.TaskDecomposerTest  any /com.aicodingcli.conversation.TaskDecomposerTest  assertFalse /com.aicodingcli.conversation.TaskDecomposerTest  assertNotEquals /com.aicodingcli.conversation.TaskDecomposerTest  
assertNotNull /com.aicodingcli.conversation.TaskDecomposerTest  
assertTrue /com.aicodingcli.conversation.TaskDecomposerTest  contains /com.aicodingcli.conversation.TaskDecomposerTest  distinct /com.aicodingcli.conversation.TaskDecomposerTest  	emptyList /com.aicodingcli.conversation.TaskDecomposerTest  find /com.aicodingcli.conversation.TaskDecomposerTest  first /com.aicodingcli.conversation.TaskDecomposerTest  
isNotEmpty /com.aicodingcli.conversation.TaskDecomposerTest  listOf /com.aicodingcli.conversation.TaskDecomposerTest  map /com.aicodingcli.conversation.TaskDecomposerTest  mapOf /com.aicodingcli.conversation.TaskDecomposerTest  runTest /com.aicodingcli.conversation.TaskDecomposerTest  sorted /com.aicodingcli.conversation.TaskDecomposerTest  taskDecomposer /com.aicodingcli.conversation.TaskDecomposerTest  to /com.aicodingcli.conversation.TaskDecomposerTest  
parameters %com.aicodingcli.conversation.ToolCall  toolName %com.aicodingcli.conversation.ToolCall  errors -com.aicodingcli.conversation.ValidationResult  isValid -com.aicodingcli.conversation.ValidationResult  not kotlin.Boolean  
Collection kotlin.collections  distinct kotlin.collections  find kotlin.collections  sorted kotlin.collections  any kotlin.collections.Collection  distinct kotlin.collections.List  find kotlin.collections.List  sorted kotlin.collections.List  values kotlin.collections.Map  distinct kotlin.sequences  find kotlin.sequences  sorted kotlin.sequences  find kotlin.text  ExecutableTask !kotlinx.coroutines.test.TestScope  ProjectContext !kotlinx.coroutines.test.TestScope  ToolCall !kotlinx.coroutines.test.TestScope  any !kotlinx.coroutines.test.TestScope  assertNotEquals !kotlinx.coroutines.test.TestScope  distinct !kotlinx.coroutines.test.TestScope  find !kotlinx.coroutines.test.TestScope  first !kotlinx.coroutines.test.TestScope  map !kotlinx.coroutines.test.TestScope  sorted !kotlinx.coroutines.test.TestScope  taskDecomposer !kotlinx.coroutines.test.TestScope  DefaultTaskDecomposer org.junit.jupiter.api  ExecutableTask org.junit.jupiter.api  ProjectContext org.junit.jupiter.api  TaskDecomposer org.junit.jupiter.api  ToolCall org.junit.jupiter.api  assertNotEquals org.junit.jupiter.api  distinct org.junit.jupiter.api  find org.junit.jupiter.api  sorted org.junit.jupiter.api  taskDecomposer org.junit.jupiter.api  assertNotEquals  org.junit.jupiter.api.Assertions  println com.aicodingcli.conversation  println /com.aicodingcli.conversation.TaskDecomposerTest  println !kotlinx.coroutines.test.TestScope  println org.junit.jupiter.api                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         