/ Header Record For PersistentHashMapValueStorage3 2src/test/kotlin/com/aicodingcli/AiCodingCliTest.kt6 5src/test/kotlin/com/aicodingcli/AnalyzeCommandTest.kt< ;src/test/kotlin/com/aicodingcli/ai/AiRequestResponseTest.kt4 3src/test/kotlin/com/aicodingcli/ai/AiServiceTest.ktB Asrc/test/kotlin/com/aicodingcli/ai/providers/ClaudeServiceTest.ktB Asrc/test/kotlin/com/aicodingcli/ai/providers/OllamaServiceTest.ktB Asrc/test/kotlin/com/aicodingcli/ai/providers/OpenAiServiceTest.kt2 1src/test/kotlin/com/aicodingcli/code/DebugTest.ktA @src/test/kotlin/com/aicodingcli/code/ProjectAnalysisDebugTest.ktH Gsrc/test/kotlin/com/aicodingcli/code/analysis/CodeAnalysisModelsTest.ktB Asrc/test/kotlin/com/aicodingcli/code/analysis/CodeAnalyzerTest.ktG Fsrc/test/kotlin/com/aicodingcli/code/common/ProgrammingLanguageTest.ktN Msrc/test/kotlin/com/aicodingcli/code/metrics/ImprovedMetricsCalculatorTest.ktA @src/test/kotlin/com/aicodingcli/code/metrics/MetricsDebugTest.ktL Ksrc/test/kotlin/com/aicodingcli/code/quality/ImprovedQualityAnalyzerTest.ktD Csrc/test/kotlin/com/aicodingcli/code/quality/QualityAnalyzerTest.kt< ;src/test/kotlin/com/aicodingcli/config/ConfigManagerTest.ktP Osrc/test/kotlin/com/aicodingcli/conversation/AiDrivenAutoExecutionEngineTest.ktC Bsrc/test/kotlin/com/aicodingcli/conversation/AiPromptEngineTest.ktH Gsrc/test/kotlin/com/aicodingcli/conversation/AutoExecutionEngineTest.ktM Lsrc/test/kotlin/com/aicodingcli/conversation/ConversationStateManagerTest.kt> =src/test/kotlin/com/aicodingcli/conversation/MockAiService.ktF Esrc/test/kotlin/com/aicodingcli/conversation/RequirementParserTest.ktC Bsrc/test/kotlin/com/aicodingcli/conversation/TaskDecomposerTest.ktA @src/test/kotlin/com/aicodingcli/conversation/ToolExecutorTest.kt> =src/test/kotlin/com/aicodingcli/history/HistoryManagerTest.kt= <src/test/kotlin/com/aicodingcli/history/HistoryModelsTest.kt7 6src/test/kotlin/com/aicodingcli/http/HttpClientTest.kt? >src/test/kotlin/com/aicodingcli/plugins/AiServicePluginTest.kt= <src/test/kotlin/com/aicodingcli/plugins/CommandPluginTest.kt= <src/test/kotlin/com/aicodingcli/plugins/PluginManagerTest.kt> =src/test/kotlin/com/aicodingcli/plugins/PluginSecurityTest.kt6 5src/test/kotlin/com/aicodingcli/plugins/PluginTest.ktC Bsrc/test/kotlin/com/aicodingcli/conversation/AiPromptEngineTest.ktC Bsrc/test/kotlin/com/aicodingcli/conversation/AiPromptEngineTest.kt