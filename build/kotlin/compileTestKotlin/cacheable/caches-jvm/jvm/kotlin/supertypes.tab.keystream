*com.aicodingcli.conversation.MockAiService2com.aicodingcli.conversation.ScenarioMockAiService)com.aicodingcli.conversation.TestScenario+com.aicodingcli.plugins.TestAiServicePlugin%com.aicodingcli.plugins.TestAiService)com.aicodingcli.plugins.TestCommandPlugin/com.aicodingcli.integration.SimpleMockAiService                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            