<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.aicodingcli.AnalyzeCommandTest" tests="6" skipped="0" failures="0" errors="0" timestamp="2025-06-17T23:41:04.209Z" hostname="zxnapdeMacBook-Pro.local" time="0.027">
  <properties/>
  <testcase name="should handle analyze issues command()" classname="com.aicodingcli.AnalyzeCommandTest" time="0.012"/>
  <testcase name="should handle json format output()" classname="com.aicodingcli.AnalyzeCommandTest" time="0.005"/>
  <testcase name="should handle analyze file command()" classname="com.aicodingcli.AnalyzeCommandTest" time="0.002"/>
  <testcase name="should handle analyze help command()" classname="com.aicodingcli.AnalyzeCommandTest" time="0.0"/>
  <testcase name="should handle analyze project command()" classname="com.aicodingcli.AnalyzeCommandTest" time="0.004"/>
  <testcase name="should handle analyze metrics command()" classname="com.aicodingcli.AnalyzeCommandTest" time="0.002"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
