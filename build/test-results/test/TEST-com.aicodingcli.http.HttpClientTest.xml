<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.aicodingcli.http.HttpClientTest" tests="8" skipped="0" failures="0" errors="0" timestamp="2025-06-17T23:41:05.013Z" hostname="zxnapdeMacBook-Pro.local" time="0.473">
  <properties/>
  <testcase name="should fail after max retries exceeded()" classname="com.aicodingcli.http.HttpClientTest" time="0.009"/>
  <testcase name="should make successful POST request with JSON body()" classname="com.aicodingcli.http.HttpClientTest" time="0.003"/>
  <testcase name="should add custom headers()" classname="com.aicodingcli.http.HttpClientTest" time="0.002"/>
  <testcase name="should make successful GET request()" classname="com.aicodingcli.http.HttpClientTest" time="0.002"/>
  <testcase name="should handle timeout()" classname="com.aicodingcli.http.HttpClientTest" time="0.447"/>
  <testcase name="should retry on network errors()" classname="com.aicodingcli.http.HttpClientTest" time="0.004"/>
  <testcase name="should handle HTTP error responses()" classname="com.aicodingcli.http.HttpClientTest" time="0.002"/>
  <testcase name="should handle rate limiting with retry after()" classname="com.aicodingcli.http.HttpClientTest" time="0.002"/>
  <system-out><![CDATA[07:41:05.016 [Test worker @kotlinx.coroutines.test runner#184] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
07:41:05.019 [Test worker @kotlinx.coroutines.test runner#184] INFO io.ktor.client.HttpClient -- REQUEST https://api.example.com/test failed with exception: java.lang.Exception: Persistent network error
07:41:05.020 [Test worker @kotlinx.coroutines.test runner#184] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
07:41:05.021 [Test worker @kotlinx.coroutines.test runner#184] INFO io.ktor.client.HttpClient -- REQUEST https://api.example.com/test failed with exception: java.lang.Exception: Persistent network error
07:41:05.021 [Test worker @kotlinx.coroutines.test runner#184] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
07:41:05.022 [Test worker @kotlinx.coroutines.test runner#184] INFO io.ktor.client.HttpClient -- REQUEST https://api.example.com/test failed with exception: java.lang.Exception: Persistent network error
07:41:05.024 [Test worker @kotlinx.coroutines.test runner#192] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/create
METHOD: HttpMethod(value=POST)
07:41:05.025 [Test worker @kotlinx.coroutines.test runner#192] INFO io.ktor.client.HttpClient -- RESPONSE: 201 Created
METHOD: HttpMethod(value=POST)
FROM: https://api.example.com/create
07:41:05.027 [Test worker @kotlinx.coroutines.test runner#196] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
07:41:05.027 [Test worker @kotlinx.coroutines.test runner#196] INFO io.ktor.client.HttpClient -- RESPONSE: 200 OK
METHOD: HttpMethod(value=GET)
FROM: https://api.example.com/test
07:41:05.028 [Test worker @kotlinx.coroutines.test runner#200] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
07:41:05.029 [Test worker @kotlinx.coroutines.test runner#200] INFO io.ktor.client.HttpClient -- RESPONSE: 200 OK
METHOD: HttpMethod(value=GET)
FROM: https://api.example.com/test
07:41:05.030 [Test worker @kotlinx.coroutines.test runner#204] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/slow
METHOD: HttpMethod(value=GET)
07:41:05.141 [Test worker @kotlinx.coroutines.test runner#204] INFO io.ktor.client.HttpClient -- REQUEST https://api.example.com/slow failed with exception: java.util.concurrent.CancellationException: Request timeout has expired [url=https://api.example.com/slow, request_timeout=100 ms]
07:41:05.142 [Test worker @kotlinx.coroutines.test runner#204] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/slow
METHOD: HttpMethod(value=GET)
07:41:05.254 [Test worker @kotlinx.coroutines.test runner#204] INFO io.ktor.client.HttpClient -- REQUEST https://api.example.com/slow failed with exception: java.util.concurrent.CancellationException: Request timeout has expired [url=https://api.example.com/slow, request_timeout=100 ms]
07:41:05.257 [Test worker @kotlinx.coroutines.test runner#204] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/slow
METHOD: HttpMethod(value=GET)
07:41:05.364 [Test worker @kotlinx.coroutines.test runner#204] INFO io.ktor.client.HttpClient -- REQUEST https://api.example.com/slow failed with exception: java.util.concurrent.CancellationException: Request timeout has expired [url=https://api.example.com/slow, request_timeout=100 ms]
07:41:05.365 [Test worker @kotlinx.coroutines.test runner#204] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/slow
METHOD: HttpMethod(value=GET)
07:41:05.476 [Test worker @kotlinx.coroutines.test runner#204] INFO io.ktor.client.HttpClient -- REQUEST https://api.example.com/slow failed with exception: java.util.concurrent.CancellationException: Request timeout has expired [url=https://api.example.com/slow, request_timeout=100 ms]
07:41:05.479 [Test worker @kotlinx.coroutines.test runner#214] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
07:41:05.479 [Test worker @kotlinx.coroutines.test runner#214] INFO io.ktor.client.HttpClient -- REQUEST https://api.example.com/test failed with exception: java.lang.Exception: Network error
07:41:05.480 [Test worker @kotlinx.coroutines.test runner#214] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
07:41:05.480 [Test worker @kotlinx.coroutines.test runner#214] INFO io.ktor.client.HttpClient -- REQUEST https://api.example.com/test failed with exception: java.lang.Exception: Network error
07:41:05.481 [Test worker @kotlinx.coroutines.test runner#214] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
07:41:05.481 [Test worker @kotlinx.coroutines.test runner#214] INFO io.ktor.client.HttpClient -- RESPONSE: 200 OK
METHOD: HttpMethod(value=GET)
FROM: https://api.example.com/test
07:41:05.483 [Test worker @kotlinx.coroutines.test runner#222] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/notfound
METHOD: HttpMethod(value=GET)
07:41:05.483 [Test worker @kotlinx.coroutines.test runner#222] INFO io.ktor.client.HttpClient -- RESPONSE: 404 Not Found
METHOD: HttpMethod(value=GET)
FROM: https://api.example.com/notfound
07:41:05.485 [Test worker @kotlinx.coroutines.test runner#226] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
07:41:05.485 [Test worker @kotlinx.coroutines.test runner#226] INFO io.ktor.client.HttpClient -- RESPONSE: 429 Too Many Requests
METHOD: HttpMethod(value=GET)
FROM: https://api.example.com/test
07:41:05.486 [Test worker @kotlinx.coroutines.test runner#226] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
07:41:05.486 [Test worker @kotlinx.coroutines.test runner#226] INFO io.ktor.client.HttpClient -- RESPONSE: 200 OK
METHOD: HttpMethod(value=GET)
FROM: https://api.example.com/test
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
