<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.aicodingcli.plugins.PluginManagerTest" tests="15" skipped="0" failures="0" errors="0" timestamp="2025-06-17T23:41:05.500Z" hostname="zxnapdeMacBook-Pro.local" time="0.012">
  <properties/>
  <testcase name="should handle plugin exceptions()" classname="com.aicodingcli.plugins.PluginManagerTest" time="0.001"/>
  <testcase name="should handle event dispatcher operations()" classname="com.aicodingcli.plugins.PluginManagerTest" time="0.001"/>
  <testcase name="should create plugin manager with correct configuration()" classname="com.aicodingcli.plugins.PluginManagerTest" time="0.0"/>
  <testcase name="should handle plugin registry operations()" classname="com.aicodingcli.plugins.PluginManagerTest" time="0.0"/>
  <testcase name="should handle plugin info()" classname="com.aicodingcli.plugins.PluginManagerTest" time="0.001"/>
  <testcase name="should handle registry statistics()" classname="com.aicodingcli.plugins.PluginManagerTest" time="0.0"/>
  <testcase name="should handle plugin operations that are not yet implemented()" classname="com.aicodingcli.plugins.PluginManagerTest" time="0.001"/>
  <testcase name="should create plugin discovery service()" classname="com.aicodingcli.plugins.PluginManagerTest" time="0.001"/>
  <testcase name="should get plugin correctly()" classname="com.aicodingcli.plugins.PluginManagerTest" time="0.001"/>
  <testcase name="should get plugin state correctly()" classname="com.aicodingcli.plugins.PluginManagerTest" time="0.0"/>
  <testcase name="should clear registry correctly()" classname="com.aicodingcli.plugins.PluginManagerTest" time="0.001"/>
  <testcase name="should validate plugin file correctly()" classname="com.aicodingcli.plugins.PluginManagerTest" time="0.0"/>
  <testcase name="should handle uninstall of non-existent plugin()" classname="com.aicodingcli.plugins.PluginManagerTest" time="0.001"/>
  <testcase name="should handle plugin validation result()" classname="com.aicodingcli.plugins.PluginManagerTest" time="0.001"/>
  <testcase name="should handle unload of non-existent plugin()" classname="com.aicodingcli.plugins.PluginManagerTest" time="0.001"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
