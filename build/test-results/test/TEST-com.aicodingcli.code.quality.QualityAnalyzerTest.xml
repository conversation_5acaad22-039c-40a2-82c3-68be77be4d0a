<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.aicodingcli.code.quality.QualityAnalyzerTest" tests="1" skipped="0" failures="0" errors="0" timestamp="2025-06-17T23:41:04.769Z" hostname="zxnapdeMacBook-Pro.local" time="0.0">
  <properties/>
  <testcase name="should suggest improvements for complex nested conditions()" classname="com.aicodingcli.code.quality.QualityAnalyzerTest" time="0.0"/>
  <system-out><![CDATA[Number of suggestions: 2
- MAINTAINABILITY: Consider extracting complex conditional logic into separate methods
- READABILITY: Consider adding documentation for public methods
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
