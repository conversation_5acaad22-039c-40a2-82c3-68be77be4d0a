<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.aicodingcli.plugins.PluginSecurityTest" tests="10" skipped="0" failures="0" errors="0" timestamp="2025-06-17T23:41:05.512Z" hostname="zxnapdeMacBook-Pro.local" time="0.007">
  <properties/>
  <testcase name="should validate config and history access()" classname="com.aicodingcli.plugins.PluginSecurityTest" time="0.002"/>
  <testcase name="should check plugin permissions correctly()" classname="com.aicodingcli.plugins.PluginSecurityTest" time="0.001"/>
  <testcase name="should handle sandbox removal()" classname="com.aicodingcli.plugins.PluginSecurityTest" time="0.0"/>
  <testcase name="should validate file access permissions()" classname="com.aicodingcli.plugins.PluginSecurityTest" time="0.001"/>
  <testcase name="should validate command execution permissions()" classname="com.aicodingcli.plugins.PluginSecurityTest" time="0.001"/>
  <testcase name="should create default security policy()" classname="com.aicodingcli.plugins.PluginSecurityTest" time="0.0"/>
  <testcase name="should validate permission requests()" classname="com.aicodingcli.plugins.PluginSecurityTest" time="0.001"/>
  <testcase name="should handle plugin security exceptions()" classname="com.aicodingcli.plugins.PluginSecurityTest" time="0.0"/>
  <testcase name="should validate network access permissions()" classname="com.aicodingcli.plugins.PluginSecurityTest" time="0.001"/>
  <testcase name="should create sandbox with correct permissions()" classname="com.aicodingcli.plugins.PluginSecurityTest" time="0.0"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
