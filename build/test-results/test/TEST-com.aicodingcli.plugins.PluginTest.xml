<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.aicodingcli.plugins.PluginTest" tests="9" skipped="0" failures="0" errors="0" timestamp="2025-06-17T23:41:05.520Z" hostname="zxnapdeMacBook-Pro.local" time="0.002">
  <properties/>
  <testcase name="should create plugin validation result()" classname="com.aicodingcli.plugins.PluginTest" time="0.0"/>
  <testcase name="should create plugin context correctly()" classname="com.aicodingcli.plugins.PluginTest" time="0.0"/>
  <testcase name="should create plugin metadata with dependencies and permissions()" classname="com.aicodingcli.plugins.PluginTest" time="0.0"/>
  <testcase name="should handle plugin states()" classname="com.aicodingcli.plugins.PluginTest" time="0.001"/>
  <testcase name="should create plugin load exception()" classname="com.aicodingcli.plugins.PluginTest" time="0.0"/>
  <testcase name="should handle shared data in context()" classname="com.aicodingcli.plugins.PluginTest" time="0.0"/>
  <testcase name="should create different types of permissions()" classname="com.aicodingcli.plugins.PluginTest" time="0.0"/>
  <testcase name="should create plugin execution exception()" classname="com.aicodingcli.plugins.PluginTest" time="0.0"/>
  <testcase name="should create plugin metadata correctly()" classname="com.aicodingcli.plugins.PluginTest" time="0.0"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
