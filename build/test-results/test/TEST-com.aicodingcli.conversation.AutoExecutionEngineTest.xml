<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.aicodingcli.conversation.AutoExecutionEngineTest" tests="1" skipped="0" failures="1" errors="0" timestamp="2025-06-18T01:57:35.773Z" hostname="zxnapdeMacBook-Pro.local" time="0.091">
  <properties/>
  <testcase name="should provide detailed execution summary()" classname="com.aicodingcli.conversation.AutoExecutionEngineTest" time="0.091">
    <failure message="org.opentest4j.AssertionFailedError: Execution should be successful ==&gt; expected: &lt;true&gt; but was: &lt;false&gt;" type="org.opentest4j.AssertionFailedError">org.opentest4j.AssertionFailedError: Execution should be successful ==&gt; expected: &lt;true&gt; but was: &lt;false&gt;
	at app//org.junit.jupiter.api.AssertionFailureBuilder.build(AssertionFailureBuilder.java:151)
	at app//org.junit.jupiter.api.AssertionFailureBuilder.buildAndThrow(AssertionFailureBuilder.java:132)
	at app//org.junit.jupiter.api.AssertTrue.failNotTrue(AssertTrue.java:63)
	at app//org.junit.jupiter.api.AssertTrue.assertTrue(AssertTrue.java:36)
	at app//org.junit.jupiter.api.Assertions.assertTrue(Assertions.java:214)
	at app//com.aicodingcli.conversation.AutoExecutionEngineTest$should provide detailed execution summary$1.invokeSuspend(AutoExecutionEngineTest.kt:272)
	at app//com.aicodingcli.conversation.AutoExecutionEngineTest$should provide detailed execution summary$1.invoke(AutoExecutionEngineTest.kt)
	at app//com.aicodingcli.conversation.AutoExecutionEngineTest$should provide detailed execution summary$1.invoke(AutoExecutionEngineTest.kt)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt$runTest$2$1$1.invokeSuspend(TestBuilders.kt:316)
	at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at app//kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at app//kotlinx.coroutines.test.TestDispatcher.processEvent$kotlinx_coroutines_test(TestDispatcher.kt:24)
	at app//kotlinx.coroutines.test.TestCoroutineScheduler.tryRunNextTaskUnless$kotlinx_coroutines_test(TestCoroutineScheduler.kt:99)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt$runTest$2$1$workRunner$1.invokeSuspend(TestBuilders.kt:322)
	at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at app//kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at app//kotlinx.coroutines.EventLoopImplBase.processNextEvent(EventLoop.common.kt:277)
	at app//kotlinx.coroutines.BlockingCoroutine.joinBlocking(Builders.kt:95)
	at app//kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking(Builders.kt:69)
	at app//kotlinx.coroutines.BuildersKt.runBlocking(Unknown Source)
	at app//kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking$default(Builders.kt:48)
	at app//kotlinx.coroutines.BuildersKt.runBlocking$default(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersJvmKt.createTestResult(TestBuildersJvm.kt:10)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0(TestBuilders.kt:310)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0(TestBuilders.kt:168)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0$default(TestBuilders.kt:160)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0$default(Unknown Source)
	at app//com.aicodingcli.conversation.AutoExecutionEngineTest.should provide detailed execution summary(AutoExecutionEngineTest.kt:258)
	at java.base@23.0.2/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base@23.0.2/java.util.ArrayList.forEach(ArrayList.java:1597)
	at java.base@23.0.2/java.util.ArrayList.forEach(ArrayList.java:1597)
</failure>
  </testcase>
  <system-out><![CDATA[DEBUG: result.success = false
DEBUG: result.sessionId = 5fa2a29b-9e84-449e-9f2d-aa9c1d484f0c
DEBUG: result.summary = null
DEBUG: result.executionTime = 36.541041ms
DEBUG: result.executedSteps.size = 1
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
