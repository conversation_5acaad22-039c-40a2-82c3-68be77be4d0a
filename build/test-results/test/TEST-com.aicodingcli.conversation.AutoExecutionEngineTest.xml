<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.aicodingcli.conversation.AutoExecutionEngineTest" tests="1" skipped="0" failures="1" errors="0" timestamp="2025-06-18T01:47:58.352Z" hostname="zxnapdeMacBook-Pro.local" time="0.098">
  <properties/>
  <testcase name="should execute simple conversation successfully()" classname="com.aicodingcli.conversation.AutoExecutionEngineTest" time="0.098">
    <failure message="org.opentest4j.AssertionFailedError: expected: &lt;true&gt; but was: &lt;false&gt;" type="org.opentest4j.AssertionFailedError">org.opentest4j.AssertionFailedError: expected: &lt;true&gt; but was: &lt;false&gt;
	at app//org.junit.jupiter.api.AssertionFailureBuilder.build(AssertionFailureBuilder.java:151)
	at app//org.junit.jupiter.api.AssertionFailureBuilder.buildAndThrow(AssertionFailureBuilder.java:132)
	at app//org.junit.jupiter.api.AssertTrue.failNotTrue(AssertTrue.java:63)
	at app//org.junit.jupiter.api.AssertTrue.assertTrue(AssertTrue.java:36)
	at app//org.junit.jupiter.api.AssertTrue.assertTrue(AssertTrue.java:31)
	at app//org.junit.jupiter.api.Assertions.assertTrue(Assertions.java:183)
	at app//com.aicodingcli.conversation.AutoExecutionEngineTest$should execute simple conversation successfully$1.invokeSuspend(AutoExecutionEngineTest.kt:73)
	at app//com.aicodingcli.conversation.AutoExecutionEngineTest$should execute simple conversation successfully$1.invoke(AutoExecutionEngineTest.kt)
	at app//com.aicodingcli.conversation.AutoExecutionEngineTest$should execute simple conversation successfully$1.invoke(AutoExecutionEngineTest.kt)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt$runTest$2$1$1.invokeSuspend(TestBuilders.kt:316)
	at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at app//kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at app//kotlinx.coroutines.test.TestDispatcher.processEvent$kotlinx_coroutines_test(TestDispatcher.kt:24)
	at app//kotlinx.coroutines.test.TestCoroutineScheduler.tryRunNextTaskUnless$kotlinx_coroutines_test(TestCoroutineScheduler.kt:99)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt$runTest$2$1$workRunner$1.invokeSuspend(TestBuilders.kt:322)
	at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at app//kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at app//kotlinx.coroutines.EventLoopImplBase.processNextEvent(EventLoop.common.kt:277)
	at app//kotlinx.coroutines.BlockingCoroutine.joinBlocking(Builders.kt:95)
	at app//kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking(Builders.kt:69)
	at app//kotlinx.coroutines.BuildersKt.runBlocking(Unknown Source)
	at app//kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking$default(Builders.kt:48)
	at app//kotlinx.coroutines.BuildersKt.runBlocking$default(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersJvmKt.createTestResult(TestBuildersJvm.kt:10)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0(TestBuilders.kt:310)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0(TestBuilders.kt:168)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0$default(TestBuilders.kt:160)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0$default(Unknown Source)
	at app//com.aicodingcli.conversation.AutoExecutionEngineTest.should execute simple conversation successfully(AutoExecutionEngineTest.kt:47)
	at java.base@23.0.2/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base@23.0.2/java.util.ArrayList.forEach(ArrayList.java:1597)
	at java.base@23.0.2/java.util.ArrayList.forEach(ArrayList.java:1597)
</failure>
  </testcase>
  <system-out><![CDATA[DEBUG AutoExecutionEngine: About to call taskDecomposer.decompose
DEBUG TaskDecomposer: requirement='Create a simple User data class with name and email properties', requirementLower='create a simple user data class with name and email properties'
DEBUG TaskDecomposer: isSimpleClassCreation=true
DEBUG TaskDecomposer: isRestApiRequirement=false
DEBUG TaskDecomposer: isConfigurationRequirement=true
DEBUG TaskDecomposer: isDataModelRequirement=true
DEBUG TaskDecomposer: Matched isSimpleClassCreation
DEBUG TaskDecomposer: Generated 1 tasks
DEBUG AutoExecutionEngine: taskDecomposer.decompose returned 1 tasks
DEBUG AutoExecutionEngine: About to call executeTasksInSession with 1 tasks
DEBUG executeTasksInSession: currentSession.tasks.size = 1
DEBUG executeTasksInSession: Executing task: Create Data class file with 1 tool calls
Result success: true
Final status: COMPLETED
Executed steps count: 1
Execution rounds: 1
Error: null
Step 0: save-file - true
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
