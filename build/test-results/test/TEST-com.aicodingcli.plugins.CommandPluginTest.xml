<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.aicodingcli.plugins.CommandPluginTest" tests="13" skipped="0" failures="0" errors="0" timestamp="2025-06-17T23:41:05.493Z" hostname="zxnapdeMacBook-Pro.local" time="0.006">
  <properties/>
  <testcase name="should execute echo command with options()" classname="com.aicodingcli.plugins.CommandPluginTest" time="0.002"/>
  <testcase name="should provide commands()" classname="com.aicodingcli.plugins.CommandPluginTest" time="0.0"/>
  <testcase name="should create command result helpers()" classname="com.aicodingcli.plugins.CommandPluginTest" time="0.001"/>
  <testcase name="should create command options correctly()" classname="com.aicodingcli.plugins.CommandPluginTest" time="0.0"/>
  <testcase name="should execute echo command with arguments()" classname="com.aicodingcli.plugins.CommandPluginTest" time="0.0"/>
  <testcase name="should check if command exists()" classname="com.aicodingcli.plugins.CommandPluginTest" time="0.0"/>
  <testcase name="should create command plugin with correct metadata()" classname="com.aicodingcli.plugins.CommandPluginTest" time="0.0"/>
  <testcase name="should execute hello command successfully()" classname="com.aicodingcli.plugins.CommandPluginTest" time="0.0"/>
  <testcase name="should validate plugin successfully()" classname="com.aicodingcli.plugins.CommandPluginTest" time="0.0"/>
  <testcase name="should shutdown plugin correctly()" classname="com.aicodingcli.plugins.CommandPluginTest" time="0.0"/>
  <testcase name="should initialize and register commands()" classname="com.aicodingcli.plugins.CommandPluginTest" time="0.0"/>
  <testcase name="should handle command arguments correctly()" classname="com.aicodingcli.plugins.CommandPluginTest" time="0.0"/>
  <testcase name="should handle command not found()" classname="com.aicodingcli.plugins.CommandPluginTest" time="0.001"/>
  <system-out><![CDATA[[2025-06-18 07:41:05] [DEBUG] [Plugin:test-command-plugin] Registered test command: hello
[2025-06-18 07:41:05] [DEBUG] [Plugin:test-command-plugin] Registered test command: echo
[2025-06-18 07:41:05] [INFO] [Plugin:test-command-plugin] Command plugin Test Command Plugin initialized with 2 commands
[2025-06-18 07:41:05] [DEBUG] [Plugin:test-command-plugin] Registered test command: hello
[2025-06-18 07:41:05] [DEBUG] [Plugin:test-command-plugin] Registered test command: echo
[2025-06-18 07:41:05] [INFO] [Plugin:test-command-plugin] Command plugin Test Command Plugin initialized with 2 commands
[2025-06-18 07:41:05] [DEBUG] [Plugin:test-command-plugin] Registered test command: hello
[2025-06-18 07:41:05] [DEBUG] [Plugin:test-command-plugin] Registered test command: echo
[2025-06-18 07:41:05] [INFO] [Plugin:test-command-plugin] Command plugin Test Command Plugin initialized with 2 commands
[2025-06-18 07:41:05] [DEBUG] [Plugin:test-command-plugin] Registered test command: hello
[2025-06-18 07:41:05] [DEBUG] [Plugin:test-command-plugin] Registered test command: echo
[2025-06-18 07:41:05] [INFO] [Plugin:test-command-plugin] Command plugin Test Command Plugin initialized with 2 commands
[2025-06-18 07:41:05] [INFO] [Plugin:test-command-plugin] Command plugin Test Command Plugin shut down
[2025-06-18 07:41:05] [DEBUG] [Plugin:test-command-plugin] Registered test command: hello
[2025-06-18 07:41:05] [DEBUG] [Plugin:test-command-plugin] Registered test command: echo
[2025-06-18 07:41:05] [INFO] [Plugin:test-command-plugin] Command plugin Test Command Plugin initialized with 2 commands
[2025-06-18 07:41:05] [INFO] [Plugin:test-command-plugin] Command plugin Test Command Plugin shut down
[2025-06-18 07:41:05] [DEBUG] [Plugin:test-command-plugin] Registered test command: hello
[2025-06-18 07:41:05] [DEBUG] [Plugin:test-command-plugin] Registered test command: echo
[2025-06-18 07:41:05] [INFO] [Plugin:test-command-plugin] Command plugin Test Command Plugin initialized with 2 commands
[2025-06-18 07:41:05] [DEBUG] [Plugin:test-command-plugin] Registered test command: hello
[2025-06-18 07:41:05] [DEBUG] [Plugin:test-command-plugin] Registered test command: echo
[2025-06-18 07:41:05] [INFO] [Plugin:test-command-plugin] Command plugin Test Command Plugin initialized with 2 commands
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
