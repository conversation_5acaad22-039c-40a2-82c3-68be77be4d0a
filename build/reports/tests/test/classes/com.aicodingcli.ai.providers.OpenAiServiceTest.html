<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - OpenAiServiceTest</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>OpenAiServiceTest</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/com.aicodingcli.ai.providers.html">com.aicodingcli.ai.providers</a> &gt; OpenAiServiceTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">7</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.014s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div class="tab-container">
<ul class="tabLinks">
<li>
<a href="#">Tests</a>
</li>
<li>
<a href="#">Standard output</a>
</li>
</ul>
<div class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="success">should fail connection test on error()</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should handle API error response()</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should handle streaming chat request()</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should make successful chat request()</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should test connection successfully()</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should use custom base URL if provided()</td>
<td class="success">0.005s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should validate request before sending()</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
</table>
</div>
<div class="tab">
<h2>Standard output</h2>
<span class="code">
<pre>07:41:04.727 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#29
07:41:04.728 [Test worker @kotlinx.coroutines.test runner#136] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #29#30
07:41:04.730 [Test worker @kotlinx.coroutines.test runner#136] DEBUG io.mockk.impl.recording.states.AnsweringState -- Answering HttpResponse(status=200 OK, body={
    &quot;id&quot;: &quot;chatcmpl-test&quot;,
    &quot;object&quot;: &quot;chat.completion&quot;,
    &quot;created&quot;: 1677652288,
    &quot;model&quot;: &quot;gpt-3.5-turbo&quot;,
    &quot;choices&quot;: [
        {
            &quot;index&quot;: 0,
            &quot;message&quot;: {
                &quot;role&quot;: &quot;assistant&quot;,
                &quot;content&quot;: &quot;Hi&quot;
            },
            &quot;finish_reason&quot;: &quot;stop&quot;
        }
    ],
    &quot;usage&quot;: {
        &quot;prompt_tokens&quot;: 1,
        &quot;completion_tokens&quot;: 1,
        &quot;total_tokens&quot;: 2
    }
}, headers={}) on <EMAIL>(https://custom.openai.com/v1/chat/completions, {&quot;model&quot;:&quot;gpt-3.5-turbo&quot;,&quot;messages&quot;:[{&quot;role&quot;:&quot;user&quot;,&quot;content&quot;:&quot;Hello&quot;}],&quot;temperature&quot;:0.7,&quot;max_tokens&quot;:1000}, {Authorization=Bearer test-api-key, Content-Type=application/json}, continuation {})
07:41:04.733 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#31
07:41:04.734 [Test worker @kotlinx.coroutines.test runner#142] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #31#32
07:41:04.734 [Test worker @kotlinx.coroutines.test runner#142] DEBUG io.mockk.impl.recording.states.AnsweringState -- Answering HttpResponse(status=200 OK, body={&quot;object&quot;: &quot;list&quot;, &quot;data&quot;: [{&quot;id&quot;: &quot;gpt-3.5-turbo&quot;, &quot;object&quot;: &quot;model&quot;, &quot;created&quot;: 1677610602, &quot;owned_by&quot;: &quot;openai&quot;}]}, headers={Content-Type=application/json}) on <EMAIL>(https://api.openai.com/v1/models, {Authorization=Bearer test-api-key, Content-Type=application/json}, continuation {})
07:41:04.735 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#33
07:41:04.736 [Test worker @kotlinx.coroutines.test runner#146] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #33#34
07:41:04.736 [Test worker @kotlinx.coroutines.test runner#146] DEBUG io.mockk.impl.recording.states.AnsweringState -- Throwing com.aicodingcli.http.HttpException: HTTP error 401: <NAME_EMAIL>(https://api.openai.com/v1/models, {Authorization=Bearer test-api-key, Content-Type=application/json}, continuation {})
07:41:04.737 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#35
07:41:04.738 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#36
07:41:04.738 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#37
07:41:04.739 [Test worker @kotlinx.coroutines.test runner#154] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #37#38
07:41:04.739 [Test worker @kotlinx.coroutines.test runner#154] DEBUG io.mockk.impl.recording.states.AnsweringState -- Answering HttpResponse(status=200 OK, body={
    &quot;id&quot;: &quot;chatcmpl-123&quot;,
    &quot;object&quot;: &quot;chat.completion&quot;,
    &quot;created&quot;: 1677652288,
    &quot;model&quot;: &quot;gpt-3.5-turbo&quot;,
    &quot;choices&quot;: [
        {
            &quot;index&quot;: 0,
            &quot;message&quot;: {
                &quot;role&quot;: &quot;assistant&quot;,
                &quot;content&quot;: &quot;Hello! How can I help you today?&quot;
            },
            &quot;finish_reason&quot;: &quot;stop&quot;
        }
    ],
    &quot;usage&quot;: {
        &quot;prompt_tokens&quot;: 9,
        &quot;completion_tokens&quot;: 12,
        &quot;total_tokens&quot;: 21
    }
}, headers={Content-Type=application/json}) on <EMAIL>(https://api.openai.com/v1/chat/completions, {&quot;model&quot;:&quot;gpt-3.5-turbo&quot;,&quot;messages&quot;:[{&quot;role&quot;:&quot;user&quot;,&quot;content&quot;:&quot;Hello, AI!&quot;}],&quot;temperature&quot;:0.7,&quot;max_tokens&quot;:1000}, {Authorization=Bearer test-api-key, Content-Type=application/json}, continuation {})
07:41:04.741 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#39
07:41:04.741 [Test worker @kotlinx.coroutines.test runner#160] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #39#40
07:41:04.742 [Test worker @kotlinx.coroutines.test runner#160] DEBUG io.mockk.impl.recording.states.AnsweringState -- Throwing com.aicodingcli.http.HttpException: HTTP error 401: <NAME_EMAIL>(https://api.openai.com/v1/chat/completions, {&quot;model&quot;:&quot;gpt-3.5-turbo&quot;,&quot;messages&quot;:[{&quot;role&quot;:&quot;user&quot;,&quot;content&quot;:&quot;Hello&quot;}],&quot;temperature&quot;:0.7,&quot;max_tokens&quot;:1000}, {Authorization=Bearer test-api-key, Content-Type=application/json}, continuation {})
</pre>
</span>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="https://www.gradle.org">Gradle 8.14.1</a> at 2025年6月18日 07:41:05</p>
</div>
</div>
</body>
</html>
