<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - AiDrivenAutoExecutionEngineTest</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>AiDrivenAutoExecutionEngineTest</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/com.aicodingcli.conversation.html">com.aicodingcli.conversation</a> &gt; AiDrivenAutoExecutionEngineTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">12</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">5</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.131s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox failures" id="successRate">
<div class="percent">58%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div class="tab-container">
<ul class="tabLinks">
<li>
<a href="#">Failed tests</a>
</li>
<li>
<a href="#">Tests</a>
</li>
</ul>
<div class="tab">
<h2>Failed tests</h2>
<div class="test">
<a name="should continue existing conversation()"></a>
<h3 class="failures">should continue existing conversation()</h3>
<span class="code">
<pre>org.opentest4j.AssertionFailedError: expected: &lt;true&gt; but was: &lt;false&gt;
	at app//org.junit.jupiter.api.AssertionFailureBuilder.build(AssertionFailureBuilder.java:151)
	at app//org.junit.jupiter.api.AssertionFailureBuilder.buildAndThrow(AssertionFailureBuilder.java:132)
	at app//org.junit.jupiter.api.AssertTrue.failNotTrue(AssertTrue.java:63)
	at app//org.junit.jupiter.api.AssertTrue.assertTrue(AssertTrue.java:36)
	at app//org.junit.jupiter.api.AssertTrue.assertTrue(AssertTrue.java:31)
	at app//org.junit.jupiter.api.Assertions.assertTrue(Assertions.java:183)
	at app//com.aicodingcli.conversation.AiDrivenAutoExecutionEngineTest$should continue existing conversation$1.invokeSuspend(AiDrivenAutoExecutionEngineTest.kt:193)
	at app//com.aicodingcli.conversation.AiDrivenAutoExecutionEngineTest$should continue existing conversation$1.invoke(AiDrivenAutoExecutionEngineTest.kt)
	at app//com.aicodingcli.conversation.AiDrivenAutoExecutionEngineTest$should continue existing conversation$1.invoke(AiDrivenAutoExecutionEngineTest.kt)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt$runTest$2$1$1.invokeSuspend(TestBuilders.kt:316)
	at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at app//kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at app//kotlinx.coroutines.test.TestDispatcher.processEvent$kotlinx_coroutines_test(TestDispatcher.kt:24)
	at app//kotlinx.coroutines.test.TestCoroutineScheduler.tryRunNextTaskUnless$kotlinx_coroutines_test(TestCoroutineScheduler.kt:99)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt$runTest$2$1$workRunner$1.invokeSuspend(TestBuilders.kt:322)
	at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at app//kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at app//kotlinx.coroutines.EventLoopImplBase.processNextEvent(EventLoop.common.kt:277)
	at app//kotlinx.coroutines.BlockingCoroutine.joinBlocking(Builders.kt:95)
	at app//kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking(Builders.kt:69)
	at app//kotlinx.coroutines.BuildersKt.runBlocking(Unknown Source)
	at app//kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking$default(Builders.kt:48)
	at app//kotlinx.coroutines.BuildersKt.runBlocking$default(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersJvmKt.createTestResult(TestBuildersJvm.kt:10)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0(TestBuilders.kt:310)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0(TestBuilders.kt:168)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0$default(TestBuilders.kt:160)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0$default(Unknown Source)
	at app//com.aicodingcli.conversation.AiDrivenAutoExecutionEngineTest.should continue existing conversation(AiDrivenAutoExecutionEngineTest.kt:180)
	at java.base@23.0.2/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base@23.0.2/java.util.ArrayList.forEach(ArrayList.java:1597)
	at java.base@23.0.2/java.util.ArrayList.forEach(ArrayList.java:1597)
</pre>
</span>
</div>
<div class="test">
<a name="should execute multi-step requirement()"></a>
<h3 class="failures">should execute multi-step requirement()</h3>
<span class="code">
<pre>org.opentest4j.AssertionFailedError: expected: &lt;true&gt; but was: &lt;false&gt;
	at app//org.junit.jupiter.api.AssertionFailureBuilder.build(AssertionFailureBuilder.java:151)
	at app//org.junit.jupiter.api.AssertionFailureBuilder.buildAndThrow(AssertionFailureBuilder.java:132)
	at app//org.junit.jupiter.api.AssertTrue.failNotTrue(AssertTrue.java:63)
	at app//org.junit.jupiter.api.AssertTrue.assertTrue(AssertTrue.java:36)
	at app//org.junit.jupiter.api.AssertTrue.assertTrue(AssertTrue.java:31)
	at app//org.junit.jupiter.api.Assertions.assertTrue(Assertions.java:183)
	at app//com.aicodingcli.conversation.AiDrivenAutoExecutionEngineTest$should execute multi-step requirement$1.invokeSuspend(AiDrivenAutoExecutionEngineTest.kt:100)
	at app//com.aicodingcli.conversation.AiDrivenAutoExecutionEngineTest$should execute multi-step requirement$1.invoke(AiDrivenAutoExecutionEngineTest.kt)
	at app//com.aicodingcli.conversation.AiDrivenAutoExecutionEngineTest$should execute multi-step requirement$1.invoke(AiDrivenAutoExecutionEngineTest.kt)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt$runTest$2$1$1.invokeSuspend(TestBuilders.kt:316)
	at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at app//kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at app//kotlinx.coroutines.test.TestDispatcher.processEvent$kotlinx_coroutines_test(TestDispatcher.kt:24)
	at app//kotlinx.coroutines.test.TestCoroutineScheduler.tryRunNextTaskUnless$kotlinx_coroutines_test(TestCoroutineScheduler.kt:99)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt$runTest$2$1$workRunner$1.invokeSuspend(TestBuilders.kt:322)
	at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at app//kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at app//kotlinx.coroutines.EventLoopImplBase.processNextEvent(EventLoop.common.kt:277)
	at app//kotlinx.coroutines.BlockingCoroutine.joinBlocking(Builders.kt:95)
	at app//kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking(Builders.kt:69)
	at app//kotlinx.coroutines.BuildersKt.runBlocking(Unknown Source)
	at app//kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking$default(Builders.kt:48)
	at app//kotlinx.coroutines.BuildersKt.runBlocking$default(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersJvmKt.createTestResult(TestBuildersJvm.kt:10)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0(TestBuilders.kt:310)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0(TestBuilders.kt:168)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0$default(TestBuilders.kt:160)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0$default(Unknown Source)
	at app//com.aicodingcli.conversation.AiDrivenAutoExecutionEngineTest.should execute multi-step requirement(AiDrivenAutoExecutionEngineTest.kt:82)
	at java.base@23.0.2/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base@23.0.2/java.util.ArrayList.forEach(ArrayList.java:1597)
	at java.base@23.0.2/java.util.ArrayList.forEach(ArrayList.java:1597)
</pre>
</span>
</div>
<div class="test">
<a name="should execute simple requirement successfully()"></a>
<h3 class="failures">should execute simple requirement successfully()</h3>
<span class="code">
<pre>org.opentest4j.AssertionFailedError: expected: &lt;true&gt; but was: &lt;false&gt;
	at app//org.junit.jupiter.api.AssertionFailureBuilder.build(AssertionFailureBuilder.java:151)
	at app//org.junit.jupiter.api.AssertionFailureBuilder.buildAndThrow(AssertionFailureBuilder.java:132)
	at app//org.junit.jupiter.api.AssertTrue.failNotTrue(AssertTrue.java:63)
	at app//org.junit.jupiter.api.AssertTrue.assertTrue(AssertTrue.java:36)
	at app//org.junit.jupiter.api.AssertTrue.assertTrue(AssertTrue.java:31)
	at app//org.junit.jupiter.api.Assertions.assertTrue(Assertions.java:183)
	at app//com.aicodingcli.conversation.AiDrivenAutoExecutionEngineTest$should execute simple requirement successfully$1.invokeSuspend(AiDrivenAutoExecutionEngineTest.kt:53)
	at app//com.aicodingcli.conversation.AiDrivenAutoExecutionEngineTest$should execute simple requirement successfully$1.invoke(AiDrivenAutoExecutionEngineTest.kt)
	at app//com.aicodingcli.conversation.AiDrivenAutoExecutionEngineTest$should execute simple requirement successfully$1.invoke(AiDrivenAutoExecutionEngineTest.kt)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt$runTest$2$1$1.invokeSuspend(TestBuilders.kt:316)
	at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at app//kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at app//kotlinx.coroutines.test.TestDispatcher.processEvent$kotlinx_coroutines_test(TestDispatcher.kt:24)
	at app//kotlinx.coroutines.test.TestCoroutineScheduler.tryRunNextTaskUnless$kotlinx_coroutines_test(TestCoroutineScheduler.kt:99)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt$runTest$2$1$workRunner$1.invokeSuspend(TestBuilders.kt:322)
	at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at app//kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at app//kotlinx.coroutines.EventLoopImplBase.processNextEvent(EventLoop.common.kt:277)
	at app//kotlinx.coroutines.BlockingCoroutine.joinBlocking(Builders.kt:95)
	at app//kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking(Builders.kt:69)
	at app//kotlinx.coroutines.BuildersKt.runBlocking(Unknown Source)
	at app//kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking$default(Builders.kt:48)
	at app//kotlinx.coroutines.BuildersKt.runBlocking$default(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersJvmKt.createTestResult(TestBuildersJvm.kt:10)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0(TestBuilders.kt:310)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0(TestBuilders.kt:168)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0$default(TestBuilders.kt:160)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0$default(Unknown Source)
	at app//com.aicodingcli.conversation.AiDrivenAutoExecutionEngineTest.should execute simple requirement successfully(AiDrivenAutoExecutionEngineTest.kt:42)
	at java.base@23.0.2/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base@23.0.2/java.util.ArrayList.forEach(ArrayList.java:1597)
	at java.base@23.0.2/java.util.ArrayList.forEach(ArrayList.java:1597)
</pre>
</span>
</div>
<div class="test">
<a name="should require user confirmation for dangerous operations()"></a>
<h3 class="failures">should require user confirmation for dangerous operations()</h3>
<span class="code">
<pre>org.opentest4j.AssertionFailedError: expected: &lt;true&gt; but was: &lt;false&gt;
	at app//org.junit.jupiter.api.AssertionFailureBuilder.build(AssertionFailureBuilder.java:151)
	at app//org.junit.jupiter.api.AssertionFailureBuilder.buildAndThrow(AssertionFailureBuilder.java:132)
	at app//org.junit.jupiter.api.AssertTrue.failNotTrue(AssertTrue.java:63)
	at app//org.junit.jupiter.api.AssertTrue.assertTrue(AssertTrue.java:36)
	at app//org.junit.jupiter.api.AssertTrue.assertTrue(AssertTrue.java:31)
	at app//org.junit.jupiter.api.Assertions.assertTrue(Assertions.java:183)
	at app//com.aicodingcli.conversation.AiDrivenAutoExecutionEngineTest$should require user confirmation for dangerous operations$1.invokeSuspend(AiDrivenAutoExecutionEngineTest.kt:129)
	at app//com.aicodingcli.conversation.AiDrivenAutoExecutionEngineTest$should require user confirmation for dangerous operations$1.invoke(AiDrivenAutoExecutionEngineTest.kt)
	at app//com.aicodingcli.conversation.AiDrivenAutoExecutionEngineTest$should require user confirmation for dangerous operations$1.invoke(AiDrivenAutoExecutionEngineTest.kt)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt$runTest$2$1$1.invokeSuspend(TestBuilders.kt:316)
	at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at app//kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at app//kotlinx.coroutines.test.TestDispatcher.processEvent$kotlinx_coroutines_test(TestDispatcher.kt:24)
	at app//kotlinx.coroutines.test.TestCoroutineScheduler.tryRunNextTaskUnless$kotlinx_coroutines_test(TestCoroutineScheduler.kt:99)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt$runTest$2$1$workRunner$1.invokeSuspend(TestBuilders.kt:322)
	at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at app//kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at app//kotlinx.coroutines.EventLoopImplBase.processNextEvent(EventLoop.common.kt:277)
	at app//kotlinx.coroutines.BlockingCoroutine.joinBlocking(Builders.kt:95)
	at app//kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking(Builders.kt:69)
	at app//kotlinx.coroutines.BuildersKt.runBlocking(Unknown Source)
	at app//kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking$default(Builders.kt:48)
	at app//kotlinx.coroutines.BuildersKt.runBlocking$default(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersJvmKt.createTestResult(TestBuildersJvm.kt:10)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0(TestBuilders.kt:310)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0(TestBuilders.kt:168)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0$default(TestBuilders.kt:160)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0$default(Unknown Source)
	at app//com.aicodingcli.conversation.AiDrivenAutoExecutionEngineTest.should require user confirmation for dangerous operations(AiDrivenAutoExecutionEngineTest.kt:110)
	at java.base@23.0.2/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base@23.0.2/java.util.ArrayList.forEach(ArrayList.java:1597)
	at java.base@23.0.2/java.util.ArrayList.forEach(ArrayList.java:1597)
</pre>
</span>
</div>
<div class="test">
<a name="should update session state during execution()"></a>
<h3 class="failures">should update session state during execution()</h3>
<span class="code">
<pre>org.opentest4j.AssertionFailedError: expected: &lt;COMPLETED&gt; but was: &lt;EXECUTING&gt;
	at app//org.junit.jupiter.api.AssertionFailureBuilder.build(AssertionFailureBuilder.java:151)
	at app//org.junit.jupiter.api.AssertionFailureBuilder.buildAndThrow(AssertionFailureBuilder.java:132)
	at app//org.junit.jupiter.api.AssertEquals.failNotEqual(AssertEquals.java:197)
	at app//org.junit.jupiter.api.AssertEquals.assertEquals(AssertEquals.java:182)
	at app//org.junit.jupiter.api.AssertEquals.assertEquals(AssertEquals.java:177)
	at app//org.junit.jupiter.api.Assertions.assertEquals(Assertions.java:1145)
	at app//com.aicodingcli.conversation.AiDrivenAutoExecutionEngineTest$should update session state during execution$1.invokeSuspend(AiDrivenAutoExecutionEngineTest.kt:322)
	at app//com.aicodingcli.conversation.AiDrivenAutoExecutionEngineTest$should update session state during execution$1.invoke(AiDrivenAutoExecutionEngineTest.kt)
	at app//com.aicodingcli.conversation.AiDrivenAutoExecutionEngineTest$should update session state during execution$1.invoke(AiDrivenAutoExecutionEngineTest.kt)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt$runTest$2$1$1.invokeSuspend(TestBuilders.kt:316)
	at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at app//kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at app//kotlinx.coroutines.test.TestDispatcher.processEvent$kotlinx_coroutines_test(TestDispatcher.kt:24)
	at app//kotlinx.coroutines.test.TestCoroutineScheduler.tryRunNextTaskUnless$kotlinx_coroutines_test(TestCoroutineScheduler.kt:99)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt$runTest$2$1$workRunner$1.invokeSuspend(TestBuilders.kt:322)
	at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at app//kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at app//kotlinx.coroutines.EventLoopImplBase.processNextEvent(EventLoop.common.kt:277)
	at app//kotlinx.coroutines.BlockingCoroutine.joinBlocking(Builders.kt:95)
	at app//kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking(Builders.kt:69)
	at app//kotlinx.coroutines.BuildersKt.runBlocking(Unknown Source)
	at app//kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking$default(Builders.kt:48)
	at app//kotlinx.coroutines.BuildersKt.runBlocking$default(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersJvmKt.createTestResult(TestBuildersJvm.kt:10)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0(TestBuilders.kt:310)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0(TestBuilders.kt:168)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0$default(TestBuilders.kt:160)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0$default(Unknown Source)
	at app//com.aicodingcli.conversation.AiDrivenAutoExecutionEngineTest.should update session state during execution(AiDrivenAutoExecutionEngineTest.kt:309)
	at java.base@23.0.2/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base@23.0.2/java.util.ArrayList.forEach(ArrayList.java:1597)
	at java.base@23.0.2/java.util.ArrayList.forEach(ArrayList.java:1597)
</pre>
</span>
</div>
</div>
<div class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="failures">should continue existing conversation()</td>
<td class="failures">0.008s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="failures">should execute multi-step requirement()</td>
<td class="failures">0.004s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="failures">should execute simple requirement successfully()</td>
<td class="failures">0.001s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="success">should execute single step successfully()</td>
<td class="success">0.003s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should fail to continue non-existent conversation()</td>
<td class="success">0.054s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should handle AI service failures gracefully()</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should handle empty requirement gracefully()</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="failures">should require user confirmation for dangerous operations()</td>
<td class="failures">0.002s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="success">should respect tool restrictions in strategy()</td>
<td class="success">0.040s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should stop at maximum execution rounds()</td>
<td class="success">0.012s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="failures">should update session state during execution()</td>
<td class="failures">0.002s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="success">should validate safety checks for dangerous operations()</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
</table>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="https://www.gradle.org">Gradle 8.14.1</a> at 2025年6月18日 10:44:40</p>
</div>
</div>
</body>
</html>
