<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - CommandPluginTest</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>CommandPluginTest</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/com.aicodingcli.plugins.html">com.aicodingcli.plugins</a> &gt; CommandPluginTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">13</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.004s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div class="tab-container">
<ul class="tabLinks">
<li>
<a href="#">Tests</a>
</li>
<li>
<a href="#">Standard output</a>
</li>
</ul>
<div class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="success">should check if command exists()</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should create command options correctly()</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should create command plugin with correct metadata()</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should create command result helpers()</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should execute echo command with arguments()</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should execute echo command with options()</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should execute hello command successfully()</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should handle command arguments correctly()</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should handle command not found()</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should initialize and register commands()</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should provide commands()</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should shutdown plugin correctly()</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should validate plugin successfully()</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
</table>
</div>
<div class="tab">
<h2>Standard output</h2>
<span class="code">
<pre>[2025-06-18 07:41:05] [DEBUG] [Plugin:test-command-plugin] Registered test command: hello
[2025-06-18 07:41:05] [DEBUG] [Plugin:test-command-plugin] Registered test command: echo
[2025-06-18 07:41:05] [INFO] [Plugin:test-command-plugin] Command plugin Test Command Plugin initialized with 2 commands
[2025-06-18 07:41:05] [DEBUG] [Plugin:test-command-plugin] Registered test command: hello
[2025-06-18 07:41:05] [DEBUG] [Plugin:test-command-plugin] Registered test command: echo
[2025-06-18 07:41:05] [INFO] [Plugin:test-command-plugin] Command plugin Test Command Plugin initialized with 2 commands
[2025-06-18 07:41:05] [DEBUG] [Plugin:test-command-plugin] Registered test command: hello
[2025-06-18 07:41:05] [DEBUG] [Plugin:test-command-plugin] Registered test command: echo
[2025-06-18 07:41:05] [INFO] [Plugin:test-command-plugin] Command plugin Test Command Plugin initialized with 2 commands
[2025-06-18 07:41:05] [DEBUG] [Plugin:test-command-plugin] Registered test command: hello
[2025-06-18 07:41:05] [DEBUG] [Plugin:test-command-plugin] Registered test command: echo
[2025-06-18 07:41:05] [INFO] [Plugin:test-command-plugin] Command plugin Test Command Plugin initialized with 2 commands
[2025-06-18 07:41:05] [INFO] [Plugin:test-command-plugin] Command plugin Test Command Plugin shut down
[2025-06-18 07:41:05] [DEBUG] [Plugin:test-command-plugin] Registered test command: hello
[2025-06-18 07:41:05] [DEBUG] [Plugin:test-command-plugin] Registered test command: echo
[2025-06-18 07:41:05] [INFO] [Plugin:test-command-plugin] Command plugin Test Command Plugin initialized with 2 commands
[2025-06-18 07:41:05] [INFO] [Plugin:test-command-plugin] Command plugin Test Command Plugin shut down
[2025-06-18 07:41:05] [DEBUG] [Plugin:test-command-plugin] Registered test command: hello
[2025-06-18 07:41:05] [DEBUG] [Plugin:test-command-plugin] Registered test command: echo
[2025-06-18 07:41:05] [INFO] [Plugin:test-command-plugin] Command plugin Test Command Plugin initialized with 2 commands
[2025-06-18 07:41:05] [DEBUG] [Plugin:test-command-plugin] Registered test command: hello
[2025-06-18 07:41:05] [DEBUG] [Plugin:test-command-plugin] Registered test command: echo
[2025-06-18 07:41:05] [INFO] [Plugin:test-command-plugin] Command plugin Test Command Plugin initialized with 2 commands
</pre>
</span>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="https://www.gradle.org">Gradle 8.14.1</a> at 2025年6月18日 07:41:05</p>
</div>
</div>
</body>
</html>
