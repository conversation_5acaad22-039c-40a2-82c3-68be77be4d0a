<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - ProjectAnalysisDebugTest</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>ProjectAnalysisDebugTest</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/com.aicodingcli.code.html">com.aicodingcli.code</a> &gt; ProjectAnalysisDebugTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">1</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.002s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div class="tab-container">
<ul class="tabLinks">
<li>
<a href="#">Tests</a>
</li>
<li>
<a href="#">Standard output</a>
</li>
</ul>
<div class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="success">debug project analysis error()</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
</table>
</div>
<div class="tab">
<h2>Standard output</h2>
<span class="code">
<pre>=== PROJECT ANALYSIS DEBUG ===
Project path: /var/folders/58/mctmb5rs4m36zm0x9zdhfy480000gn/T/junit18356408637425450893
Files analyzed: 2
File: /var/folders/58/mctmb5rs4m36zm0x9zdhfy480000gn/T/junit18356408637425450893/Class1.kt
  LOC: 3
  Complexity: 1
  Maintainability: 92.50693855665945
File: /var/folders/58/mctmb5rs4m36zm0x9zdhfy480000gn/T/junit18356408637425450893/Class2.kt
  LOC: 3
  Complexity: 1
  Maintainability: 92.50693855665945
Overall metrics:
  Total LOC: 6
  Avg Complexity: 1
  Avg Maintainability: 92.50693855665945
Summary:
  Total Files: 2
  Average Complexity: 1.0
  Overall Maintainability: 92.50693855665945
</pre>
</span>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="https://www.gradle.org">Gradle 8.14.1</a> at 2025年6月18日 07:41:05</p>
</div>
</div>
</body>
</html>
