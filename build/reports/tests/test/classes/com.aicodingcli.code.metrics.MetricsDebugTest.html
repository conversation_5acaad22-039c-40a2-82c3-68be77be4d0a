<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - MetricsDebugTest</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>MetricsDebugTest</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/com.aicodingcli.code.metrics.html">com.aicodingcli.code.metrics</a> &gt; MetricsDebugTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">2</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.002s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div class="tab-container">
<ul class="tabLinks">
<li>
<a href="#">Tests</a>
</li>
<li>
<a href="#">Standard output</a>
</li>
</ul>
<div class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="success">debug cyclomatic complexity calculation()</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">debug lines of code calculation()</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
</table>
</div>
<div class="tab">
<h2>Standard output</h2>
<span class="code">
<pre>=== LINES OF CODE DEBUG ===
Code:
// This is a comment
class TestClass {
    // Another comment
    
    fun method1() {
        // Method comment
        val x = 1 // Inline comment
        return x
    }
    
    /*
     * Block comment
     * Multiple lines
     */
    fun method2() {
        val y = 2
        return y
    }
}

Calculated LOC: 10
Expected LOC: 7 (class declaration, 2 method declarations, 4 statements)
Line 1: '// This is a comment' -&gt; SKIP
Line 2: 'class TestClass {' -&gt; CODE
Line 3: '// Another comment' -&gt; SKIP
Line 4: '' -&gt; SKIP
Line 5: 'fun method1() {' -&gt; CODE
Line 6: '// Method comment' -&gt; SKIP
Line 7: 'val x = 1 // Inline comment' -&gt; CODE
Line 8: 'return x' -&gt; CODE
Line 9: '}' -&gt; CODE
Line 10: '' -&gt; SKIP
Line 11: '/*' -&gt; SKIP
Line 12: '* Block comment' -&gt; SKIP
Line 13: '* Multiple lines' -&gt; SKIP
Line 14: '*/' -&gt; SKIP
Line 15: 'fun method2() {' -&gt; CODE
Line 16: 'val y = 2' -&gt; CODE
Line 17: 'return y' -&gt; CODE
Line 18: '}' -&gt; CODE
Line 19: '}' -&gt; CODE
=== CYCLOMATIC COMPLEXITY DEBUG ===
Code:
class ConditionalClass {
    fun processInput(input: String?): String {
        if (input == null) {
            return &quot;null&quot;
        }
        
        if (input.isEmpty()) {
            return &quot;empty&quot;
        }
        
        return when {
            input.length &gt; 10 -&gt; &quot;long&quot;
            input.length &gt; 5 -&gt; &quot;medium&quot;
            else -&gt; &quot;short&quot;
        }
    }
}

Calculated complexity: 5
Expected complexity: 6 (2 if statements + 3 when branches + 1 base)
Lines of code: 15
Duplicated lines: 0
</pre>
</span>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="https://www.gradle.org">Gradle 8.14.1</a> at 2025年6月18日 07:41:05</p>
</div>
</div>
</body>
</html>
