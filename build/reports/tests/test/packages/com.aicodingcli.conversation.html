<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - Package com.aicodingcli.conversation</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Package com.aicodingcli.conversation</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; com.aicodingcli.conversation</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">12</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">5</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.131s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox failures" id="successRate">
<div class="percent">58%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div class="tab-container">
<ul class="tabLinks">
<li>
<a href="#">Failed tests</a>
</li>
<li>
<a href="#">Classes</a>
</li>
</ul>
<div class="tab">
<h2>Failed tests</h2>
<ul class="linkList">
<li>
<a href="../classes/com.aicodingcli.conversation.AiDrivenAutoExecutionEngineTest.html">AiDrivenAutoExecutionEngineTest</a>.
<a href="../classes/com.aicodingcli.conversation.AiDrivenAutoExecutionEngineTest.html#should continue existing conversation()">should continue existing conversation()</a>
</li>
<li>
<a href="../classes/com.aicodingcli.conversation.AiDrivenAutoExecutionEngineTest.html">AiDrivenAutoExecutionEngineTest</a>.
<a href="../classes/com.aicodingcli.conversation.AiDrivenAutoExecutionEngineTest.html#should execute multi-step requirement()">should execute multi-step requirement()</a>
</li>
<li>
<a href="../classes/com.aicodingcli.conversation.AiDrivenAutoExecutionEngineTest.html">AiDrivenAutoExecutionEngineTest</a>.
<a href="../classes/com.aicodingcli.conversation.AiDrivenAutoExecutionEngineTest.html#should execute simple requirement successfully()">should execute simple requirement successfully()</a>
</li>
<li>
<a href="../classes/com.aicodingcli.conversation.AiDrivenAutoExecutionEngineTest.html">AiDrivenAutoExecutionEngineTest</a>.
<a href="../classes/com.aicodingcli.conversation.AiDrivenAutoExecutionEngineTest.html#should require user confirmation for dangerous operations()">should require user confirmation for dangerous operations()</a>
</li>
<li>
<a href="../classes/com.aicodingcli.conversation.AiDrivenAutoExecutionEngineTest.html">AiDrivenAutoExecutionEngineTest</a>.
<a href="../classes/com.aicodingcli.conversation.AiDrivenAutoExecutionEngineTest.html#should update session state during execution()">should update session state during execution()</a>
</li>
</ul>
</div>
<div class="tab">
<h2>Classes</h2>
<table>
<thead>
<tr>
<th>Class</th>
<th>Tests</th>
<th>Failures</th>
<th>Ignored</th>
<th>Duration</th>
<th>Success rate</th>
</tr>
</thead>
<tr>
<td class="failures">
<a href="../classes/com.aicodingcli.conversation.AiDrivenAutoExecutionEngineTest.html">AiDrivenAutoExecutionEngineTest</a>
</td>
<td>12</td>
<td>5</td>
<td>0</td>
<td>0.131s</td>
<td class="failures">58%</td>
</tr>
</table>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="https://www.gradle.org">Gradle 8.14.1</a> at 2025年6月18日 10:44:40</p>
</div>
</div>
</body>
</html>
